GlobalSearch::search - Query: 5000357, Extended: 0
GlobalSearch::search - Final results: Array
(
    [orders] => Array
        (
        )

    [products] => Array
        (
            [0] => Array
                (
                    [product_id] => 5000357
                    [name] =>  Възглавница White Boutique - Perla
                    [model] => 110014009HD
                    [sku] => 
                    [price] => 270.00
                    [status] => Активен
                    [image] => data/White Boutique Pillow/ гъши пух Перла.jpg
                    [edit_url] => index.php?route=catalog/product/edit&product_id=5000357&user_token=qZm231YI0ukMYSl4zEACRe67vV8cPkJ3
                )

        )

    [group_counts] => Array
        (
            [orders] => 0
            [products] => 1
        )

)

GlobalSearch::search - Query: 5000357, Extended: 0
GlobalSearch::search - Final results: Array
(
    [orders] => Array
        (
        )

    [products] => Array
        (
            [0] => Array
                (
                    [product_id] => 5000357
                    [name] =>  Възглавница White Boutique - Perla
                    [model] => 110014009HD
                    [sku] => 
                    [price] => 270.00
                    [status] => Активен
                    [image] => data/White Boutique Pillow/ гъши пух Перла.jpg
                    [edit_url] => index.php?route=catalog/product/edit&product_id=5000357&user_token=qZm231YI0ukMYSl4zEACRe67vV8cPkJ3
                )

        )

    [group_counts] => Array
        (
            [orders] => 0
            [products] => 1
        )

)

Orders::getTotalCount - SQL: 
                SELECT COUNT(*) as total
                FROM
                    oc_order o
                LEFT JOIN
                    oc_order_status os ON (o.order_status_id = os.order_status_id)
                WHERE
                    os.language_id = '2'
                    AND ((LOWER(CONCAT(o.firstname, " ", o.lastname)) LIKE '%5000357%' OR LOWER(CONCAT(o.firstname, " ", o.lastname)) LIKE '%000357%' OR LOWER(CONCAT(o.firstname, " ", o.lastname)) LIKE '%500357%' OR LOWER(CONCAT(o.firstname, " ", o.lastname)) LIKE '%500057%' OR LOWER(CONCAT(o.firstname, " ", o.lastname)) LIKE '%500037%' OR LOWER(CONCAT(o.firstname, " ", o.lastname)) LIKE '%500035%' OR LOWER(CONCAT(o.firstname, " ", o.lastname)) LIKE '%а000357%' OR LOWER(CONCAT(o.firstname, " ", o.lastname)) LIKE '%б000357%' OR LOWER(CONCAT(o.firstname, " ", o.lastname)) LIKE '%в000357%' OR LOWER(CONCAT(o.firstname, " ", o.lastname)) LIKE '%г000357%' OR LOWER(CONCAT(o.firstname, " ", o.lastname)) LIKE '%д000357%' OR LOWER(o.email) LIKE '%5000357%' OR LOWER(o.email) LIKE '%000357%' OR LOWER(o.email) LIKE '%500357%' OR LOWER(o.email) LIKE '%500057%' OR LOWER(o.email) LIKE '%500037%' OR LOWER(o.email) LIKE '%500035%' OR LOWER(o.email) LIKE '%а000357%' OR LOWER(o.email) LIKE '%б000357%' OR LOWER(o.email) LIKE '%в000357%' OR LOWER(o.email) LIKE '%г000357%' OR LOWER(o.email) LIKE '%д000357%' OR LOWER(os.name) LIKE '%5000357%' OR LOWER(os.name) LIKE '%000357%' OR LOWER(os.name) LIKE '%500357%' OR LOWER(os.name) LIKE '%500057%' OR LOWER(os.name) LIKE '%500037%' OR LOWER(os.name) LIKE '%500035%' OR LOWER(os.name) LIKE '%а000357%' OR LOWER(os.name) LIKE '%б000357%' OR LOWER(os.name) LIKE '%в000357%' OR LOWER(os.name) LIKE '%г000357%' OR LOWER(os.name) LIKE '%д000357%' OR LOWER(CONCAT(o.shipping_address_1, " ", o.shipping_city)) LIKE '%5000357%' OR LOWER(CONCAT(o.shipping_address_1, " ", o.shipping_city)) LIKE '%000357%' OR LOWER(CONCAT(o.shipping_address_1, " ", o.shipping_city)) LIKE '%500357%' OR LOWER(CONCAT(o.shipping_address_1, " ", o.shipping_city)) LIKE '%500057%' OR LOWER(CONCAT(o.shipping_address_1, " ", o.shipping_city)) LIKE '%500037%' OR LOWER(CONCAT(o.shipping_address_1, " ", o.shipping_city)) LIKE '%500035%' OR LOWER(CONCAT(o.shipping_address_1, " ", o.shipping_city)) LIKE '%а000357%' OR LOWER(CONCAT(o.shipping_address_1, " ", o.shipping_city)) LIKE '%б000357%' OR LOWER(CONCAT(o.shipping_address_1, " ", o.shipping_city)) LIKE '%в000357%' OR LOWER(CONCAT(o.shipping_address_1, " ", o.shipping_city)) LIKE '%г000357%' OR LOWER(CONCAT(o.shipping_address_1, " ", o.shipping_city)) LIKE '%д000357%' OR o.order_id LIKE '%5000357%' OR o.telephone LIKE '%5000357%'))
            
Orders::getTotalCount - Total: 0
