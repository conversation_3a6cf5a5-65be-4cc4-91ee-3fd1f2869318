GlobalSearch::performSearch - Is numeric search: yes
Orders::searchAll - Query: 5000324, Page: 1, Limit: 1000, Offset: 0
Orders::performOptimizedSearch - Query: '5000324', Limit: 1000, Offset: 0
Orders::performOptimizedSearch - DETECTED ORDER ID SEARCH: 5000324
Orders::performOptimizedSearch - Order ID search, exact matches only: 0
Orders::performOptimizedSearch - RETURNING EARLY FOR ORDER ID: []
Orders::searchAll - Results count: 0
Orders::searchAll - Order ID search detected, total: 0
Products::performOptimizedSearch - Query: '5000324', Limit: 1000, Offset: 0
Products::performOptimizedSearch - Exact matches: 0
Products::performOptimizedSearch - Partial matches: 1
Products::buildSearchConditions - Input query: '5000324'
Products::buildSearchConditions - Prepared words: 5000324
Products::buildSearchConditions - Processing word: '5000324', escaped: '5000324'
Products::buildSearchConditions - Transliterated: '5000324'
Products::buildSearchConditions - Fuzzy variants count: 10
Products::buildSearchConditions - Word condition for '5000324': (LOWER(pd.name) LIKE '%5000324%' OR LOWER(pd.name) LIKE '%000324%' OR LOWER(pd.name) LIKE '%500324%' OR LOWER(pd.name) LIKE '%500024%' OR LOWER(pd.description) LIKE '%5000324%' OR LOWER(pd.description) LIKE '%000324%' OR LOWER(pd.description) LIKE '%500324%' OR LOWER(pd.description) LIKE '%500024%' OR LOWER(p.model) LIKE '%5000324%' OR LOWER(p.model) LIKE '%000324%' OR LOWER(p.model) LIKE '%500324%' OR LOWER(p.model) LIKE '%500024%' OR LOWER(p.sku) LIKE '%5000324%' OR LOWER(p.sku) LIKE '%000324%' OR LOWER(p.sku) LIKE '%500324%' OR LOWER(p.sku) LIKE '%500024%' OR p.price LIKE '%5000324%' OR p.product_id = '5000324')
Products::buildSearchConditions - Final condition: (LOWER(pd.name) LIKE '%5000324%' OR LOWER(pd.name) LIKE '%000324%' OR LOWER(pd.name) LIKE '%500324%' OR LOWER(pd.name) LIKE '%500024%' OR LOWER(pd.description) LIKE '%5000324%' OR LOWER(pd.description) LIKE '%000324%' OR LOWER(pd.description) LIKE '%500324%' OR LOWER(pd.description) LIKE '%500024%' OR LOWER(p.model) LIKE '%5000324%' OR LOWER(p.model) LIKE '%000324%' OR LOWER(p.model) LIKE '%500324%' OR LOWER(p.model) LIKE '%500024%' OR LOWER(p.sku) LIKE '%5000324%' OR LOWER(p.sku) LIKE '%000324%' OR LOWER(p.sku) LIKE '%500324%' OR LOWER(p.sku) LIKE '%500024%' OR p.price LIKE '%5000324%' OR p.product_id = '5000324')
Products::getTotalCount - SQL: 
                SELECT COUNT(*) as total
                FROM
                    oc_product p
                LEFT JOIN
                    oc_product_description pd ON (p.product_id = pd.product_id)
                WHERE
                    pd.language_id = '2'
                    AND ((LOWER(pd.name) LIKE '%5000324%' OR LOWER(pd.name) LIKE '%000324%' OR LOWER(pd.name) LIKE '%500324%' OR LOWER(pd.name) LIKE '%500024%' OR LOWER(pd.description) LIKE '%5000324%' OR LOWER(pd.description) LIKE '%000324%' OR LOWER(pd.description) LIKE '%500324%' OR LOWER(pd.description) LIKE '%500024%' OR LOWER(p.model) LIKE '%5000324%' OR LOWER(p.model) LIKE '%000324%' OR LOWER(p.model) LIKE '%500324%' OR LOWER(p.model) LIKE '%500024%' OR LOWER(p.sku) LIKE '%5000324%' OR LOWER(p.sku) LIKE '%000324%' OR LOWER(p.sku) LIKE '%500324%' OR LOWER(p.sku) LIKE '%500024%' OR p.price LIKE '%5000324%' OR p.product_id = '5000324'))
            
Products::getTotalCount - Total: 1
GlobalSearch::renderAllSearchResults - Results: Array
(
    [orders] => Array
        (
        )

    [customers] => Array
        (
        )

    [products] => Array
        (
            [0] => Array
                (
                    [product_id] => 5000324
                    [name] =>  3D Спално бельо Памучен сатен - Айфеловата Кула
                    [model] => 101112001FS
                    [sku] => 
                    [price] => 185.00
                    [status] => 0
                    [relevance] => 50
                )

        )

    [categories] => Array
        (
        )

)

