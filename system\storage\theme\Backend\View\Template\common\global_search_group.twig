{{ header }}{{ column_left }}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="container-fluid">
                    <div class="float-right">
                        <a href="{{ url('common/globalsearch', 'user_token=' ~ user_token ~ '&query=' ~ query|url_encode) }}" 
                           data-toggle="tooltip" title="Назад към всички резултати" class="btn btn-light">
                            <i class="ri-arrow-left-line"></i>
                        </a>
                    </div>
                    <h1>{{ title }}</h1>
                    <ul class="breadcrumb">
                        <li><a href="{{ home }}">Начало</a></li>
                        <li><a href="{{ url('common/globalsearch', 'user_token=' ~ user_token ~ '&query=' ~ query|url_encode) }}">Глобално търсене</a></li>
                        <li>{{ title }}</li>
                    </ul>
                </div>
            </div>

            <div class="container-fluid">
                {% if error %}
                    <div class="alert alert-danger alert-dismissible">
                        <i class="ri-error-warning-line"></i> {{ error }}
                        <button type="button" class="close" data-dismiss="alert">&times;</button>
                    </div>
                {% endif %}

                {% if query %}
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="ri-search-line"></i> {{ title }} за: "{{ query }}"
                                {% if total > 0 %}
                                    <span class="badge badge-primary ml-2">{{ total }} резултата</span>
                                {% endif %}
                            </h3>
                        </div>
                        <div class="card-body">
                            {% if rendered_content %}
                                {{ rendered_content|raw }}
                                
                                {% if pagination %}
                                    <div class="row mt-4">
                                        <div class="col-sm-6 text-left">
                                            {{ pagination }}
                                        </div>
                                    </div>
                                {% endif %}
                            {% else %}
                                <div class="text-center py-5">
                                    <i class="ri-search-line fa-3x text-muted mb-3"></i>
                                    <h4 class="text-muted">Няма намерени резултати</h4>
                                    <p class="text-muted">Опитайте с различни ключови думи</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% else %}
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="ri-search-line fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">Няма зададена заявка за търсене</h4>
                            <p class="text-muted">Моля, въведете ключови думи за търсене</p>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{{ footer }}
