{% if products %}
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 p-6">
        {% for product in products %}
        <div class="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
            <div class="relative">
                <div class="aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-t-lg bg-gray-200">
                    {% if product.image %}
                        <img src="{{ product.image }}" alt="{{ product.name }}" class="h-48 w-full object-cover object-center">
                    {% else %}
                        <div class="h-48 w-full bg-gray-100 flex items-center justify-center">
                            <i class="ri-image-line text-gray-400 text-4xl"></i>
                        </div>
                    {% endif %}
                </div>
                <div class="absolute top-2 right-2">
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ product.status_class }}">
                        {{ product.status }}
                    </span>
                </div>
            </div>
            <div class="p-4">
                <h3 class="text-sm font-medium text-gray-900 mb-2 line-clamp-2">{{ product.name }}</h3>
                <div class="text-xs text-gray-500 mb-2">
                    <div>ID: {{ product.product_id }}</div>
                    <div>Модел: {{ product.model|default('N/A') }}</div>
                    {% if product.sku %}
                        <div>SKU: {{ product.sku }}</div>
                    {% endif %}
                </div>
                <div class="flex items-center justify-between mb-3">
                    <div class="text-sm font-medium text-gray-900">
                        {% if product.price %}
                            {{ product.price|number_format(2, '.', ',') }} лв.
                        {% else %}
                            -
                        {% endif %}
                    </div>
                    <div class="text-xs {{ product.quantity > 0 ? 'text-green-600' : 'text-red-600' }}">
                        Наличност: {{ product.quantity|default(0) }}
                    </div>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex space-x-2">
                        <a href="{{ product.view }}" class="text-blue-600 hover:text-blue-900" title="Преглед">
                            <i class="ri-eye-line"></i>
                        </a>
                        <a href="{{ product.edit }}" class="text-primary hover:text-primary/80" title="Редактирай">
                            <i class="ri-edit-line"></i>
                        </a>
                    </div>
                    <input type="checkbox" name="selected[]" value="{{ product.product_id }}" class="rounded">
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center py-8">
        <i class="ri-box-line text-gray-400 text-4xl mb-4"></i>
        <p class="text-gray-500">Няма намерени продукти</p>
    </div>
{% endif %}
