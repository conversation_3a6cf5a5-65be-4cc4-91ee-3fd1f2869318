<?php

namespace Theme25\Backend\Controller\Common\GlobalSearch;

class Products extends \Theme25\ControllerSubMethods {

     /**
     * Търсене в продукти (за dropdown) с кеширане
     */
    public function search($query) {
        $this->logDev("Products::search - Starting search for query: '$query'");

        // Проверяваме кеша първо
        $cacheKey = $this->modelSearch->generateCacheKey('products_dropdown', $query, 5);
        $cachedResults = $this->modelSearch->getCachedResults($cacheKey);

        if ($cachedResults !== null) {
            $this->logDev("Products::search - Returning cached results: " . count($cachedResults) . " items");
            return $cachedResults;
        }

        // Ако няма кеширани резултати, извършваме търсенето
        $results = $this->performOptimizedSearch($query, 5);

        // Кешираме резултатите
        $this->modelSearch->cacheResults($cacheKey, $results);

        $this->logDev("Products::search - Fresh search returned: " . count($results) . " results");
        return $results;
    }

    /**
     * Опростено търсене за debug
     */
    private function performSimpleSearch($query, $limit = 5) {
        $results = [];

        try {
            $language_id = $this->getLanguageId();
            $this->logDev("Products::performSimpleSearch - Language ID: $language_id");
            $this->logDev("Products::performSimpleSearch - DB_PREFIX: " . DB_PREFIX);

            // Първо тестваме дали има продукти изобщо
            $test_sql = "SELECT COUNT(*) as total FROM " . DB_PREFIX . "product p LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id) WHERE pd.language_id = '" . (int)$language_id . "'";
            $this->logDev("Products::performSimpleSearch - Test SQL: " . $test_sql);

            $test_result = $this->db->query($test_sql);
            $total_products = $test_result->row['total'];
            $this->logDev("Products::performSimpleSearch - Total products in DB: " . $total_products);

            if ($total_products == 0) {
                $this->logDev("Products::performSimpleSearch - No products found in database!");
                return $results;
            }

            // Тестваме без търсене - просто първите 5 продукта
            $simple_sql = "
                SELECT
                    p.product_id,
                    pd.name,
                    p.model,
                    p.sku,
                    p.price,
                    p.status
                FROM
                    " . DB_PREFIX . "product p
                LEFT JOIN
                    " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
                WHERE
                    pd.language_id = '" . (int)$language_id . "'
                ORDER BY
                    pd.name ASC
                LIMIT " . (int)$limit;

            $this->logDev("Products::performSimpleSearch - Simple SQL (no search): " . $simple_sql);

            $simple_result = $this->db->query($simple_sql);
            $this->logDev("Products::performSimpleSearch - Simple query returned: " . count($simple_result->rows) . " rows");

            if (count($simple_result->rows) > 0) {
                $this->logDev("Products::performSimpleSearch - First product name: " . $simple_result->rows[0]['name']);

                // Ако простата заявка работи, опитваме с търсене
                $escapedQuery = $this->db->escape(mb_strtolower($query));
                $this->logDev("Products::performSimpleSearch - Original query: '$query', Escaped: '$escapedQuery'");

                $search_sql = "
                    SELECT
                        p.product_id,
                        pd.name,
                        p.model,
                        p.sku,
                        p.price,
                        p.status
                    FROM
                        " . DB_PREFIX . "product p
                    LEFT JOIN
                        " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
                    WHERE
                        pd.language_id = '" . (int)$language_id . "'
                        AND (
                            LOWER(pd.name) LIKE '%{$escapedQuery}%'
                            OR LOWER(p.model) LIKE '%{$escapedQuery}%'
                            OR LOWER(p.sku) LIKE '%{$escapedQuery}%'
                        )
                    ORDER BY
                        pd.name ASC
                    LIMIT " . (int)$limit;

                $this->logDev("Products::performSimpleSearch - Search SQL: " . $search_sql);

                $query_result = $this->db->query($search_sql);
                $this->logDev("Products::performSimpleSearch - Search query returned: " . count($query_result->rows) . " rows");

                foreach ($query_result->rows as $row) {
                    $results[] = [
                        'product_id' => $row['product_id'],
                        'name' => strip_tags(html_entity_decode($row['name'], ENT_QUOTES, 'UTF-8')),
                        'model' => $row['model'] ?? '',
                        'sku' => $row['sku'] ?? '',
                        'price' => number_format((float)$row['price'], 2, '.', ''),
                        'status' => $row['status']
                    ];
                }
            } else {
                $this->logDev("Products::performSimpleSearch - Even simple query returned no results!");
            }

        } catch (Exception $e) {
            $this->logDev('Products::performSimpleSearch - Exception: ' . $e->getMessage());
            $this->logDev('Products::performSimpleSearch - Stack trace: ' . $e->getTraceAsString());
        }

        return $results;
    }


    /**
     * Рендира визуализацията на списъка с продукти
     */
    public function renderItems($results, $withPagination = false, $query = '', $page = 1, $limit = 20, $total = 0) {
        // Подготвяме данните за продуктите
        $products = [];
        foreach ($results as $product) {
            $products[] = [
                'product_id' => $product['product_id'],
                'name' => $product['name'],
                'model' => $product['model'] ?? '',
                'sku' => $product['sku'] ?? '',
                'price' => $product['price'],
                'status' => $product['status'] == 1 ? 'Активен' : 'Неактивен',
                'status_class' => $product['status'] == 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800',
                'image' => $product['image'] ?? '',
                'quantity' => $product['quantity'] ?? 0,
                'view' => $this->getAdminLink('catalog/product/view', '&product_id=' . $product['product_id']),
                'edit' => $this->getAdminLink('catalog/product/edit', '&product_id=' . $product['product_id'])
            ];
        }

        // Подготвяме данните за шаблона
        $data = [
            'products' => $products,
            'with_pagination' => $withPagination,
            'query' => $query,
            'page' => $page,
            'limit' => $limit,
            'total' => $total
        ];

        // Пагинация ако е необходима
        if ($withPagination && $total > $limit) {
            $pagination = new \Theme25\Pagination();
            $pagination->total = $total;
            $pagination->page = $page;
            $pagination->limit = $limit;
            $pagination->url = $this->getAdminLink('common/globalsearch/products', '&query=' . urlencode($query) . '&page={page}');
            $pagination->setProductText('продукта');
            $data['pagination'] = $pagination->render();
        }

        // Рендираме шаблона и връщаме HTML
        return $this->renderPartTemplate('common/global_search_products', $data);
    }

    /**
     * Оптимизирано търсене с релевантност
     */
    private function performOptimizedSearch($query, $limit = 5, $offset = 0) {
        $this->logDev("Products::performOptimizedSearch - Query: '$query', Limit: $limit, Offset: $offset");

        try {
            // Подготвяме думите за търсене
            $words = $this->modelSearch->prepareSearchWords($query);
            if (empty($words)) {
                return [];
            }

            // Търсим точни съвпадения първо
            $exactResults = $this->searchExactMatches($words, $limit);
            $this->logDev("Products::performOptimizedSearch - Exact matches: " . count($exactResults));

            // Ако имаме достатъчно точни резултати, връщаме ги
            if (count($exactResults) >= $limit) {
                return array_slice($exactResults, $offset, $limit);
            }

            // Търсим частични съвпадения за останалите места
            $remainingLimit = $limit - count($exactResults);
            $partialResults = $this->searchPartialMatches($words, $remainingLimit, $exactResults);
            $this->logDev("Products::performOptimizedSearch - Partial matches: " . count($partialResults));

            // Обединяваме резултатите
            $allResults = array_merge($exactResults, $partialResults);

            return array_slice($allResults, $offset, $limit);

        } catch (Exception $e) {
            $this->logDev('Products::performOptimizedSearch - Exception: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Търси точни съвпадения
     */
    private function searchExactMatches($words, $limit) {
        $language_id = $this->getLanguageId();
        $results = [];

        foreach ($words as $word) {
            $escapedWord = $this->db->escape(mb_strtolower($word));

            $sql = "
                SELECT DISTINCT
                    p.product_id,
                    pd.name,
                    p.model,
                    p.sku,
                    p.price,
                    p.status,
                    'exact' as match_type
                FROM
                    " . DB_PREFIX . "product p
                LEFT JOIN
                    " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
                WHERE
                    pd.language_id = '" . (int)$language_id . "'
                    AND p.status = '1'
                    AND (
                        ".
                        (is_numeric($word) ? "p.product_id = '" . (int)$word . "' OR " : "")
                        ."
                        LOWER(pd.name) = '{$escapedWord}'
                        OR LOWER(p.model) = '{$escapedWord}'
                        OR LOWER(p.sku) = '{$escapedWord}'
                    )
                ORDER BY
                    pd.name ASC
                LIMIT " . (int)$limit;

            $query_result = $this->db->query($sql);

            foreach ($query_result->rows as $row) {
                $productId = $row['product_id'];
                if (!isset($results[$productId])) {
                    $results[$productId] = [
                        'product_id' => $row['product_id'],
                        'name' => strip_tags(html_entity_decode($row['name'], ENT_QUOTES, 'UTF-8')),
                        'model' => $row['model'] ?? '',
                        'sku' => $row['sku'] ?? '',
                        'price' => number_format((float)$row['price'], 2, '.', ''),
                        'status' => $row['status'],
                        'relevance' => 100 // Най-висока релевантност за точни съвпадения
                    ];
                }
            }

            if (count($results) >= $limit) {
                break;
            }
        }

        return array_values($results);
    }

    /**
     * Търсене във всички продукти (за "виж всички" страница)
     */
    public function searchAll($query, $page = 1, $limit = 20) {
        // Проверяваме кеша първо
        $cacheKey = $this->modelSearch->generateCacheKey('products_all', $query, $limit, ($page - 1) * $limit);
        $cachedResults = $this->modelSearch->getCachedResults($cacheKey);

        if ($cachedResults !== null) {
            $this->logDev("Products::searchAll - Returning cached results");
            return $cachedResults;
        }

        $offset = ($page - 1) * $limit;
        $results = $this->performOptimizedSearch($query, $limit, $offset);

        // Добавяме общия брой резултати за пагинация
        $total = $this->getTotalCount($query);

        $searchResults = [
            'results' => $results,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];

        // Кешираме резултатите
        $this->modelSearch->cacheResults($cacheKey, $searchResults);

        return $searchResults;
    }

    /**
     * Търси частични съвпадения
     */
    private function searchPartialMatches($words, $limit, $excludeResults = []) {
        $language_id = $this->getLanguageId();
        $results = [];
        $excludeIds = array_column($excludeResults, 'product_id');

        // Строим условията за търсене
        $searchConditions = [];
        foreach ($words as $word) {
            $escapedWord = $this->db->escape(mb_strtolower($word));
            $transliteratedWord = $this->modelSearch->transliterate($word);
            $escapedTranslit = $this->db->escape(mb_strtolower($transliteratedWord));

            $wordConditions = [
                "LOWER(pd.name) LIKE '%{$escapedWord}%'",
                "LOWER(p.model) LIKE '%{$escapedWord}%'",
                "LOWER(p.sku) LIKE '%{$escapedWord}%'"
            ];

            if( is_numeric($word) ){
                $wordConditions[] = "p.product_id = '" . (int)$word . "'";
            }

            // Добавяме транслитерирани варианти ако са различни
            if ($transliteratedWord !== $word) {
                $wordConditions[] = "LOWER(pd.name) LIKE '%{$escapedTranslit}%'";
                $wordConditions[] = "LOWER(p.model) LIKE '%{$escapedTranslit}%'";
                $wordConditions[] = "LOWER(p.sku) LIKE '%{$escapedTranslit}%'";
            }

            $searchConditions[] = '(' . implode(' OR ', $wordConditions) . ')';
        }

        $whereCondition = implode(' AND ', $searchConditions);

        // Добавяме изключване на вече намерените продукти
        if (!empty($excludeIds)) {
            $excludeList = implode(',', array_map('intval', $excludeIds));
            $whereCondition .= " AND p.product_id NOT IN ({$excludeList})";
        }

        $sql = "
            SELECT DISTINCT
                p.product_id,
                pd.name,
                p.model,
                p.sku,
                p.price,
                p.status,
                'partial' as match_type
            FROM
                " . DB_PREFIX . "product p
            LEFT JOIN
                " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
            WHERE
                pd.language_id = '" . (int)$language_id . "'
                AND ({$whereCondition})
            ORDER BY
                pd.name ASC
            LIMIT " . (int)$limit;

        $query_result = $this->db->query($sql);

        foreach ($query_result->rows as $row) {
            $results[] = [
                'product_id' => $row['product_id'],
                'name' => strip_tags(html_entity_decode($row['name'], ENT_QUOTES, 'UTF-8')),
                'model' => $row['model'] ?? '',
                'sku' => $row['sku'] ?? '',
                'price' => number_format((float)$row['price'], 2, '.', ''),
                'status' => $row['status'],
                'relevance' => 50 // По-ниска релевантност за частични съвпадения
            ];
        }

        return $results;
    }

    /**
     * Основна логика за търсене
     */
    private function performSearch($query, $limit = 5, $offset = 0) {
        $results = [];

        try {
            // Debug: Логираме входните параметри
            $this->logDev("Products::performSearch - Query: '$query', Limit: $limit, Offset: $offset");

            // Подготвяме търсещите условия
            $searchConditions = $this->buildSearchConditions($query);
            $this->logDev("Products::performSearch - Search conditions: $searchConditions");

            $language_id = $this->getLanguageId();
            $this->logDev("Products::performSearch - Language ID: $language_id");

            $sql = "
                SELECT
                    p.product_id,
                    pd.name,
                    p.model,
                    p.sku,
                    p.price,
                    p.status,
                    p.image
                FROM
                    " . DB_PREFIX . "product p
                LEFT JOIN
                    " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
                WHERE
                    pd.language_id = '" . (int)$language_id . "'
                    AND ({$searchConditions})
                ORDER BY
                    pd.name ASC
                LIMIT " . (int)$limit;

            if ($offset > 0) {
                $sql .= " OFFSET " . (int)$offset;
            }

            // Debug: Логираме SQL заявката
            $this->logDev("Products::performSearch - SQL: " . $sql);

            $query_result = $this->db->query($sql);

            // Debug: Логираме резултата от заявката
            $this->logDev("Products::performSearch - Query result rows count: " . count($query_result->rows));

            if (empty($query_result->rows)) {
                $this->logDev("Products::performSearch - No rows returned from database");

                // Тестваме дали има продукти изобщо
                $test_sql = "SELECT COUNT(*) as total FROM " . DB_PREFIX . "product p LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id) WHERE pd.language_id = '" . (int)$language_id . "'";
                $test_result = $this->db->query($test_sql);
                $this->logDev("Products::performSearch - Total products in DB: " . $test_result->row['total']);
            }

            foreach ($query_result->rows as $row) {
                $results[] = [
                    'product_id' => $row['product_id'],
                    'name' => strip_tags(html_entity_decode($row['name'], ENT_QUOTES, 'UTF-8')),
                    'model' => $row['model'] ?? '',
                    'sku' => $row['sku'] ?? '',
                    'price' => number_format((float)$row['price'], 2, '.', ''),
                    'status' => $row['status'],
                    'image' => $row['image'] ?? ''
                ];
            }

        } catch (Exception $e) {
            // Логиране на грешката
            $this->logDev('Products::performSearch - Exception: ' . $e->getMessage());
            $this->logDev('Products::performSearch - Stack trace: ' . $e->getTraceAsString());
        }

        $this->logDev("Products::performSearch - Final results count: " . count($results));
        return $results;
    }

    /**
     * Получаване на общия брой резултати
     */
    private function getTotalCount($query) {
        try {
            $searchConditions = $this->buildSearchConditions($query);
            $language_id = $this->getLanguageId();

            $sql = "
                SELECT COUNT(*) as total
                FROM
                    " . DB_PREFIX . "product p
                LEFT JOIN
                    " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
                WHERE
                    pd.language_id = '" . (int)$language_id . "'
                    AND ({$searchConditions})
            ";

            $this->logDev("Products::getTotalCount - SQL: " . $sql);
            $result = $this->db->query($sql);
            $total = (int)$result->row['total'];
            $this->logDev("Products::getTotalCount - Total: " . $total);

            return $total;

        } catch (Exception $e) {
            $this->logDev('Products::getTotalCount - Exception: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Изграждане на търсещи условия с интелигентно търсене
     */
    private function buildSearchConditions($query) {
        $this->logDev("Products::buildSearchConditions - Input query: '$query'");

        $conditions = [];

        // Почистваме и разделяме заявката на думи
        $words = $this->modelSearch->prepareSearchWords($query);
        $this->logDev("Products::buildSearchConditions - Prepared words: " . implode(', ', $words));

        if (empty($words)) {
            $this->logDev("Products::buildSearchConditions - No valid words, returning 1=0");
            return "1=0"; // Няма валидни думи за търсене
        }

        // За всяка дума създаваме условия
        foreach ($words as $word) {
            $wordConditions = [];

            // Оригиналната дума
            $escapedWord = $this->db->escape(mb_strtolower($word));
            $this->logDev("Products::buildSearchConditions - Processing word: '$word', escaped: '$escapedWord'");

            // Транслитерирана версия
            $transliteratedWord = $this->modelSearch->transliterate($word);
            $escapedTranslit = $this->db->escape(mb_strtolower($transliteratedWord));
            $this->logDev("Products::buildSearchConditions - Transliterated: '$transliteratedWord'");

            // Fuzzy варианти (за думи над 4 символа)
            $fuzzyVariants = $this->modelSearch->generateFuzzyVariants($word);
            $this->logDev("Products::buildSearchConditions - Fuzzy variants count: " . count($fuzzyVariants));

            // Търсене в различни полета
            $fields = ['pd.name', 'pd.description', 'p.model', 'p.sku'];

            foreach ($fields as $field) {
                $wordConditions[] = "LOWER({$field}) LIKE '%{$escapedWord}%'";

                if ($transliteratedWord !== $word) {
                    $wordConditions[] = "LOWER({$field}) LIKE '%{$escapedTranslit}%'";
                }

                // Добавяме fuzzy варианти (ограничаваме до първите 3 за производителност)
                $limitedFuzzy = array_slice($fuzzyVariants, 0, 3);
                foreach ($limitedFuzzy as $variant) {
                    $escapedVariant = $this->db->escape(mb_strtolower($variant));
                    $wordConditions[] = "LOWER({$field}) LIKE '%{$escapedVariant}%'";
                }
            }

            // Търсене в цена и product_id (само за числови стойности)
            if (is_numeric($word)) {
                $wordConditions[] = "p.price LIKE '%{$escapedWord}%'";
                $wordConditions[] = "p.product_id = '" . (int)$word . "'"; // Точно съвпадение за product_id
            }

            // Обединяваме условията за тази дума с OR
            $wordCondition = '(' . implode(' OR ', $wordConditions) . ')';
            $conditions[] = $wordCondition;
            $this->logDev("Products::buildSearchConditions - Word condition for '$word': $wordCondition");
        }

        // Обединяваме всички думи с AND (всички думи трябва да се намират)
        $finalCondition = implode(' AND ', $conditions);
        $this->logDev("Products::buildSearchConditions - Final condition: $finalCondition");

        return $finalCondition;
    }
}
