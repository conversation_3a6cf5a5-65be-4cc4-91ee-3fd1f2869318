{% if products %}
    <div class="table-responsive">
        <table class="table table-bordered table-hover">
            <thead>
                <tr>
                    <td class="text-center">Изображение</td>
                    <td>Име на продукта</td>
                    <td>Модел</td>
                    <td>SKU</td>
                    <td class="text-right">Цена</td>
                    <td class="text-center">Количество</td>
                    <td class="text-center">Статус</td>
                    <td class="text-right">Действия</td>
                </tr>
            </thead>
            <tbody>
                {% for product in products %}
                    <tr>
                        <td class="text-center">
                            {% if product.image %}
                                <img src="{{ product.image }}" alt="{{ product.name }}" class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                            {% else %}
                                <div class="bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                    <i class="ri-image-line text-muted"></i>
                                </div>
                            {% endif %}
                        </td>
                        <td>
                            <a href="{{ product.view }}" class="text-decoration-none">
                                {{ product.name }}
                            </a>
                        </td>
                        <td>{{ product.model }}</td>
                        <td>{{ product.sku }}</td>
                        <td class="text-right">
                            {% if product.price %}
                                {{ product.price|number_format(2, '.', ',') }} лв.
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <span class="badge {% if product.quantity > 0 %}badge-success{% else %}badge-danger{% endif %}">
                                {{ product.quantity }}
                            </span>
                        </td>
                        <td class="text-center">
                            <span class="badge {{ product.status_class }}">
                                {{ product.status }}
                            </span>
                        </td>
                        <td class="text-right">
                            <div class="btn-group">
                                <a href="{{ product.view }}" 
                                   data-toggle="tooltip" title="Преглед" 
                                   class="btn btn-info btn-sm">
                                    <i class="ri-eye-line"></i>
                                </a>
                                <a href="{{ product.edit }}" 
                                   data-toggle="tooltip" title="Редактиране" 
                                   class="btn btn-primary btn-sm">
                                    <i class="ri-edit-line"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
{% else %}
    <div class="text-center py-4">
        <i class="ri-product-hunt-line fa-2x text-muted mb-2"></i>
        <p class="text-muted">Няма намерени продукти</p>
    </div>
{% endif %}
