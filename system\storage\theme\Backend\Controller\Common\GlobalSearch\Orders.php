<?php

namespace Theme25\Backend\Controller\Common\GlobalSearch;

class Orders extends \Theme25\ControllerSubMethods {

    /**
     * Унифицирано търсене в поръчки с кеширане
     */
    public function search($query, $page = 1, $limit = 20, $extendedSearch = 0) {

        // Проверяваме кеша първо
        $cacheKey = $this->modelSearch->generateCacheKey('orders_search', $query . '_ext_' . $extendedSearch . '_p' . $page . '_l' . $limit, $limit);
        $cachedResults = $this->modelSearch->getCachedResults($cacheKey);

        if ($cachedResults !== null) {
            return $cachedResults;
        }

        $offset = ($page - 1) * $limit;

        $results = [];

        $total = $this->getTotalCount($query, $extendedSearch);

        if ($total == 0) {
            return [
                'results' => [],
                'total' => 0
            ];
        }

        // Ако няма кеширани резултати, извършваме търсенето
        $results = $this->performOptimizedSearch($query, $limit, $extendedSearch, $offset);

        // За order_id търсене, общия брой е винаги 1 или 0
        // $words = $this->prepareSearchWords($query);
        // if (count($words) == 1 && is_numeric($words[0])) {
        //     $total = count($results); // За order_id търсене броят е точно колкото намерените резултати
        // } else {
        //      $total = $this->getTotalCount($query, $extendedSearch);
        // }

        $searchResults = [
            'results' => $results,
            'total' => $total
        ];

        // Кешираме резултатите
        $this->modelSearch->cacheResults($cacheKey, $searchResults);

        return $searchResults;
    }

    
    /**
     * Рендира визуализацията на списъка с поръчки
     */
    public function renderItems($results, $withPagination = false, $query = '', $page = 1, $limit = 20, $total = 0) {
        // Подготвяме данните за поръчките
        $orders = [];
        foreach ($results as $order) {
            $orders[] = [
                'order_id' => $order['order_id'],
                'customer_name' => $order['customer_name'],
                'email' => $order['email'],
                'telephone' => $order['telephone'] ?? '',
                'total' => $order['total'] . ' ' . ($order['currency'] ?? 'лв.'),
                'date_added' => $order['date_added'],
                'status' => $order['status'],
                'order_status_id' => $order['order_status_id'],
                'status_css_class' => $order['status_css_class'] ?? '',
                'payment_method' => $order['payment_method'] ?? 'N/A',
                'view' => $this->getAdminLink('sale/order/info', '&order_id=' . $order['order_id']),
                'edit' => $this->getAdminLink('sale/order/edit', '&order_id=' . $order['order_id'])
            ];
        }

        // Подготвяме данните за шаблона
        $data = [
            'orders' => $orders,
            'with_pagination' => $withPagination,
            'query' => $query,
            'page' => $page,
            'limit' => $limit,
            'total' => $total
        ];

        // Пагинация ако е необходима
        if ($withPagination && $total > $limit) {
            $pagination = new \Theme25\Pagination();
            $pagination->total = $total;
            $pagination->page = $page;
            $pagination->limit = $limit;
            $pagination->url = $this->getAdminLink('common/globalsearch/orders', '&query=' . urlencode($query) . '&page={page}');
            $pagination->setProductText('поръчки');
            $data['pagination'] = $pagination->render();
        }

        $this->setData($data);

        // Рендираме шаблона и връщаме HTML
        return $this->renderPartTemplate('common/global_search_orders');
    }

    /**
     * Оптимизирано търсене с релевантност
     */
    private function performOptimizedSearch($query, $limit = 5, $extendedSearch = 0, $offset = 0) {
        try {
            // За поръчки търсим по номер на поръчка, име на клиент, email
            $words = $this->modelSearch->prepareSearchWords($query);
            if (empty($words)) {
                return [];
            }

            // Проверяваме дали търсенето е само едно число (order_id) - ако е, връщаме САМО тази поръчка
            if (count($words) == 1 && is_numeric($words[0])) {
                $exactResults = $this->searchExactMatches($words, $limit);
                return array_slice($exactResults, $offset, $limit);
            }

            // Търсим точни съвпадения първо
            $exactResults = $this->searchExactMatches($words, $limit);

            // Ако имаме достатъчно точни резултати, връщаме ги
            if (count($exactResults) >= $limit) {
                return array_slice($exactResults, $offset, $limit);
            }

            // Търсим частични съвпадения за останалите места
            $remainingLimit = $limit - count($exactResults);
            $partialResults = $this->searchPartialMatches($words, $remainingLimit, $exactResults);

            // Обединяваме резултатите
            $allResults = array_merge($exactResults, $partialResults);

            return array_slice($allResults, $offset, $limit);

        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * Търси точни съвпадения в поръчки с интелигентна логика
     */
    private function searchExactMatches($words, $limit) {
        $results = [];

        $language_id = $this->getLanguageId();

        // Проверяваме дали търсенето е само едно число (order_id)
        if (count($words) == 1 && is_numeric($words[0])) {
            $orderId = (int)$words[0];

            $sql = "
                SELECT DISTINCT
                    o.order_id,
                    o.firstname,
                    o.lastname,
                    o.email,
                    o.telephone,
                    o.total,
                    o.currency_code,
                    o.date_added,
                    o.order_status_id,
                    os.name as status_name,
                    'exact' as match_type
                FROM
                    " . DB_PREFIX . "order o
                LEFT JOIN
                    " . DB_PREFIX . "order_status os ON (o.order_status_id = os.order_status_id)
                WHERE
                    o.order_id = '" . $orderId . "'
                    AND os.language_id = '" . (int)$language_id . "'
                ORDER BY
                    o.date_added DESC
                LIMIT " . (int)$limit;

            $query_result = $this->db->query($sql);

            foreach ($query_result->rows as $row) {
                $results[] = [
                    'order_id' => $row['order_id'],
                    'customer_name' => trim($row['firstname'] . ' ' . $row['lastname']),
                    'email' => $row['email'] ?? '',
                    'telephone' => $row['telephone'] ?? '',
                    'total' => number_format((float)$row['total'], 2, '.', ''),
                    'currency' => $row['currency_code'] ?? '',
                    'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                    'status' => $row['status_name'] ?? '',
                    'order_status_id' => $row['order_status_id'] ?? 0,
                    'status_css_class' => $this->getStatusClass($row['order_status_id'] ?? 0),
                    'relevance' => 100
                ];
            }
            // Ако намерим точно съвпадение по order_id, връщаме САМО тази поръчка
            return $results;
        }

        // Проверяваме дали търсенето е email адрес
        if (count($words) == 1 && filter_var($words[0], FILTER_VALIDATE_EMAIL)) {
            $escapedEmail = $this->db->escape(mb_strtolower($words[0]));

            $sql = "
                SELECT DISTINCT
                    o.order_id,
                    o.firstname,
                    o.lastname,
                    o.email,
                    o.telephone,
                    o.total,
                    o.currency_code,
                    o.date_added,
                    o.order_status_id,
                    os.name as status_name,
                    'exact' as match_type
                FROM
                    " . DB_PREFIX . "order o
                LEFT JOIN
                    " . DB_PREFIX . "order_status os ON (o.order_status_id = os.order_status_id)
                WHERE
                    LOWER(o.email) = '" . $escapedEmail . "'
                    AND os.language_id = '" . (int)$language_id . "'
                ORDER BY
                    o.date_added DESC
                LIMIT " . (int)$limit;

            $query_result = $this->db->query($sql);

            foreach ($query_result->rows as $row) {
                $results[] = [
                    'order_id' => $row['order_id'],
                    'customer_name' => trim($row['firstname'] . ' ' . $row['lastname']),
                    'email' => $row['email'] ?? '',
                    'telephone' => $row['telephone'] ?? '',
                    'total' => number_format((float)$row['total'], 2, '.', ''),
                    'currency' => $row['currency_code'] ?? '',
                    'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                    'status' => $row['status_name'] ?? '',
                    'order_status_id' => $row['order_status_id'] ?? 0,
                    'status_css_class' => $this->getStatusClass($row['order_status_id'] ?? 0),
                    'relevance' => 100
                ];
            }
            // Ако намерим точно съвпадение по email, връщаме ВСИЧКИ поръчки с този email
            return $results;
        }

        // Проверяваме дали търсенето е телефонен номер (само числа или с +)
        if (count($words) == 1 && preg_match('/^[\+]?[0-9\s\-\(\)]+$/', $words[0])) {
            $phone = preg_replace('/[^\+0-9]/', '', $words[0]); // Премахваме всички символи освен + и цифри
            $escapedPhone = $this->db->escape($phone);

            $sql = "
                SELECT DISTINCT
                    o.order_id,
                    o.firstname,
                    o.lastname,
                    o.email,
                    o.telephone,
                    o.total,
                    o.currency_code,
                    o.date_added,
                    o.order_status_id,
                    os.name as status_name,
                    'exact' as match_type
                FROM
                    " . DB_PREFIX . "order o
                LEFT JOIN
                    " . DB_PREFIX . "order_status os ON (o.order_status_id = os.order_status_id)
                WHERE
                    REPLACE(REPLACE(REPLACE(REPLACE(o.telephone, ' ', ''), '-', ''), '(', ''), ')', '') = '" . $escapedPhone . "'
                    AND os.language_id = '" . (int)$language_id . "'
                ORDER BY
                    o.date_added DESC
                LIMIT " . (int)$limit;

            $query_result = $this->db->query($sql);

            foreach ($query_result->rows as $row) {
                $results[] = [
                    'order_id' => $row['order_id'],
                    'customer_name' => trim($row['firstname'] . ' ' . $row['lastname']),
                    'email' => $row['email'] ?? '',
                    'telephone' => $row['telephone'] ?? '',
                    'total' => number_format((float)$row['total'], 2, '.', ''),
                    'currency' => $row['currency_code'] ?? '',
                    'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                    'status' => $row['status_name'] ?? '',
                    'order_status_id' => $row['order_status_id'] ?? 0,
                    'status_css_class' => $this->getStatusClass($row['order_status_id'] ?? 0),
                    'relevance' => 100
                ];
            }
            // Ако намерим точно съвпадение по телефон, връщаме САМО поръчките с този телефон
            return $results;
        }

        return $results;
    }

    /**
     * Търси частични съвпадения в поръчки
     */
    private function searchPartialMatches($words, $limit, $excludeResults = []) {
        $results = [];
        $excludeIds = array_column($excludeResults, 'order_id');
        $language_id = $this->getLanguageId();

        // Строим условията за търсене
        $searchConditions = [];
        foreach ($words as $word) {
            $escapedWord = $this->db->escape(mb_strtolower($word));
            $transliteratedWord = $this->modelSearch->transliterate($word);
            $escapedTranslit = $this->db->escape(mb_strtolower($transliteratedWord));

            $wordConditions = [
                "LOWER(o.firstname) LIKE '%{$escapedWord}%'",
                "LOWER(o.lastname) LIKE '%{$escapedWord}%'",
                "LOWER(o.email) LIKE '%{$escapedWord}%'",
                "LOWER(o.telephone) LIKE '%{$escapedWord}%'",
                "LOWER(o.payment_address_1) LIKE '%{$escapedWord}%'",
                "LOWER(o.payment_address_2) LIKE '%{$escapedWord}%'",
                "LOWER(o.payment_city) LIKE '%{$escapedWord}%'",
                "LOWER(o.shipping_address_1) LIKE '%{$escapedWord}%'",
                "LOWER(o.shipping_address_2) LIKE '%{$escapedWord}%'",
                "LOWER(o.shipping_city) LIKE '%{$escapedWord}%'"
            ];

            // Добавяме транслитерирани варианти ако са различни
            if ($transliteratedWord !== $word) {
                $wordConditions[] = "LOWER(o.firstname) LIKE '%{$escapedTranslit}%'";
                $wordConditions[] = "LOWER(o.lastname) LIKE '%{$escapedTranslit}%'";
                $wordConditions[] = "LOWER(o.email) LIKE '%{$escapedTranslit}%'";
            }

            $searchConditions[] = '(' . implode(' OR ', $wordConditions) . ')';
        }

        $whereCondition = implode(' AND ', $searchConditions);

        // Добавяме изключване на вече намерените поръчки
        if (!empty($excludeIds)) {
            $excludeList = implode(',', array_map('intval', $excludeIds));
            $whereCondition .= " AND o.order_id NOT IN ({$excludeList})";
        }

        $sql = "
            SELECT DISTINCT
                o.order_id,
                o.firstname,
                o.lastname,
                o.email,
                o.telephone,
                o.total,
                o.currency_code,
                o.date_added,
                o.order_status_id,
                os.name as status_name,
                'partial' as match_type
            FROM
                " . DB_PREFIX . "order o
            LEFT JOIN
                " . DB_PREFIX . "order_status os ON (o.order_status_id = os.order_status_id)
            WHERE
                os.language_id = '" . (int)$language_id . "'
                AND ({$whereCondition})
            ORDER BY
                o.date_added DESC
            LIMIT " . (int)$limit;

        $query_result = $this->db->query($sql);

        foreach ($query_result->rows as $row) {
            $results[] = [
                'order_id' => $row['order_id'],
                'customer_name' => trim($row['firstname'] . ' ' . $row['lastname']),
                'email' => $row['email'] ?? '',
                'telephone' => $row['telephone'] ?? '',
                'total' => number_format((float)$row['total'], 2, '.', ''),
                'currency' => $row['currency_code'] ?? '',
                'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                'status' => $row['status_name'] ?? '',
                'order_status_id' => $row['order_status_id'] ?? 0,
                'status_css_class' => $this->getStatusClass($row['order_status_id'] ?? 0),
                'relevance' => 50
            ];
        }

        return $results;
    }



    /**
     * Основна логика за търсене
     */
    private function performSearch($query, $limit = 5, $offset = 0) {
        $results = [];

        $language_id = $this->getLanguageId();

        try {
            // Подготвяме търсещите условия
            $searchConditions = $this->buildSearchConditions($query);

            $sql = "
                SELECT
                    o.order_id,
                    CONCAT(o.firstname, ' ', o.lastname) AS customer_name,
                    o.email,
                    o.telephone,
                    o.total,
                    o.currency_code,
                    o.date_added,
                    o.order_status_id,
                    os.name AS order_status,
                    CONCAT(o.shipping_address_1, ' ', o.shipping_city) AS shipping_address
                FROM
                    " . DB_PREFIX . "order o
                LEFT JOIN
                    " . DB_PREFIX . "order_status os ON (o.order_status_id = os.order_status_id)
                WHERE
                    os.language_id = '{$language_id}'
                    AND ({$searchConditions})
                ORDER BY
                    o.date_added DESC
                LIMIT {$limit}
            ";

            if ($offset > 0) {
                $sql .= " OFFSET {$offset}";
            }

            $query_result = $this->db->query($sql);

            foreach ($query_result->rows as $row) {
                $results[] = [
                    'order_id' => $row['order_id'],
                    'customer_name' => $row['customer_name'],
                    'email' => $row['email'],
                    'telephone' => $row['telephone'],
                    'total' => number_format((float)$row['total'], 2, '.', ''),
                    'currency_code' => $row['currency_code'],
                    'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                    'order_status' => $row['order_status'],
                    'order_status_id' => $row['order_status_id'] ?? 0,
                    'status_css_class' => $this->getStatusClass($row['order_status_id'] ?? 0),
                    'shipping_address' => $row['shipping_address']
                ];
            }

        } catch (Exception $e) {
            // Логиране на грешката
            error_log('Грешка при търсене в поръчки: ' . $e->getMessage());
        }

        return $results;
    }

    /**
     * Получаване на общия брой резултати
     * Използва същата логика като performOptimizedSearch() за консистентност
     */
    private function getTotalCount($query, $extendedSearch = 0) {
        try {
            // За поръчки търсим по номер на поръчка, име на клиент, email
            $words = $this->modelSearch->prepareSearchWords($query);
            if (empty($words)) {
                return 0;
            }

            // Проверяваме дали търсенето е само едно число (order_id) - ако е, връщаме САМО тази поръчка
            if (count($words) == 1 && is_numeric($words[0])) {
                return $this->getExactMatchesCount($words);
            }

            // Броим точните съвпадения
            $exactCount = $this->getExactMatchesCount($words);

            // Броим частичните съвпадения (без дублиране с точните)
            $partialCount = $this->getPartialMatchesCount($words, $exactCount > 0);

            return $exactCount + $partialCount;

        } catch (Exception $e) {
            error_log('Грешка при броене на поръчки: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Брои точните съвпадения
     */
    private function getExactMatchesCount($words) {
        $totalCount = 0;
        $language_id = $this->getLanguageId();

        // Проверяваме дали търсенето е order_id (само едно число)
        if (count($words) == 1 && is_numeric($words[0])) {
            $orderId = (int)$words[0];

            $sql = "
                SELECT COUNT(*) as total
                FROM " . DB_PREFIX . "order o
                LEFT JOIN " . DB_PREFIX . "order_status os ON (o.order_status_id = os.order_status_id)
                WHERE os.language_id = '" . (int)$language_id . "'
                AND o.order_id = '" . $orderId . "'";

            $result = $this->db->query($sql);
            return (int)$result->row['total'];
        }

        // Проверяваме дали търсенето е email адрес
        if (count($words) == 1 && filter_var($words[0], FILTER_VALIDATE_EMAIL)) {
            $escapedEmail = $this->db->escape(mb_strtolower($words[0]));

            $sql = "
                SELECT COUNT(*) as total
                FROM " . DB_PREFIX . "order o
                LEFT JOIN " . DB_PREFIX . "order_status os ON (o.order_status_id = os.order_status_id)
                WHERE os.language_id = '" . (int)$language_id . "'
                AND LOWER(o.email) = '{$escapedEmail}'";

            $result = $this->db->query($sql);
            $totalCount += (int)$result->row['total'];
        }

        // Проверяваме дали търсенето е телефонен номер
        if (count($words) == 1 && preg_match('/^[\d\s\+\-\(\)]+$/', $words[0])) {
            $cleanPhone = preg_replace('/[\s\-\(\)]/', '', $words[0]);
            $escapedPhone = $this->db->escape($cleanPhone);

            $sql = "
                SELECT COUNT(*) as total
                FROM " . DB_PREFIX . "order o
                LEFT JOIN " . DB_PREFIX . "order_status os ON (o.order_status_id = os.order_status_id)
                WHERE os.language_id = '" . (int)$language_id . "'
                AND REPLACE(REPLACE(REPLACE(REPLACE(o.telephone, ' ', ''), '-', ''), '(', ''), ')', '') LIKE '%{$escapedPhone}%'";

            $result = $this->db->query($sql);
            $totalCount += (int)$result->row['total'];
        }

        return $totalCount;
    }

    /**
     * Брои частичните съвпадения
     */
    private function getPartialMatchesCount($words, $hasExactMatches = false) {
        $language_id = $this->getLanguageId();

        // Строим условията за търсене
        $searchConditions = [];
        foreach ($words as $word) {
            $escapedWord = $this->db->escape(mb_strtolower($word));
            $transliteratedWord = $this->modelSearch->transliterate($word);
            $escapedTranslit = $this->db->escape(mb_strtolower($transliteratedWord));

            $wordConditions = [
                "LOWER(o.firstname) LIKE '%{$escapedWord}%'",
                "LOWER(o.lastname) LIKE '%{$escapedWord}%'",
                "LOWER(o.email) LIKE '%{$escapedWord}%'",
                "LOWER(o.telephone) LIKE '%{$escapedWord}%'"
            ];

            // Добавяме транслитерирани варианти ако са различни
            if ($transliteratedWord !== $word) {
                $wordConditions[] = "LOWER(o.firstname) LIKE '%{$escapedTranslit}%'";
                $wordConditions[] = "LOWER(o.lastname) LIKE '%{$escapedTranslit}%'";
                $wordConditions[] = "LOWER(o.email) LIKE '%{$escapedTranslit}%'";
            }

            $searchConditions[] = '(' . implode(' OR ', $wordConditions) . ')';
        }

        $whereCondition = implode(' AND ', $searchConditions);

        // Ако имаме точни съвпадения, изключваме ги от частичните
        $excludeExact = '';
        if ($hasExactMatches) {
            if (count($words) == 1 && filter_var($words[0], FILTER_VALIDATE_EMAIL)) {
                $escapedEmail = $this->db->escape(mb_strtolower($words[0]));
                $excludeExact .= " AND LOWER(o.email) != '{$escapedEmail}'";
            }
            if (count($words) == 1 && preg_match('/^[\d\s\+\-\(\)]+$/', $words[0])) {
                $cleanPhone = preg_replace('/[\s\-\(\)]/', '', $words[0]);
                $escapedPhone = $this->db->escape($cleanPhone);
                $excludeExact .= " AND REPLACE(REPLACE(REPLACE(REPLACE(o.telephone, ' ', ''), '-', ''), '(', ''), ')', '') NOT LIKE '%{$escapedPhone}%'";
            }
        }

        $sql = "
            SELECT COUNT(*) as total
            FROM " . DB_PREFIX . "order o
            LEFT JOIN " . DB_PREFIX . "order_status os ON (o.order_status_id = os.order_status_id)
            WHERE os.language_id = '" . (int)$language_id . "'
            AND ({$whereCondition})
            {$excludeExact}";

        $result = $this->db->query($sql);
        return (int)$result->row['total'];
    }

    /**
     * Изграждане на търсещи условия с интелигентно търсене
     */
    private function buildSearchConditions($query) {
        $conditions = [];

        // Почистваме и разделяме заявката на думи
        $words = $this->modelSearch->prepareSearchWords($query);

        if (empty($words)) {
            return "1=0"; // Няма валидни думи за търсене
        }

        // За всяка дума създаваме условия
        foreach ($words as $word) {
            $wordConditions = [];

            // Оригиналната дума
            $escapedWord = $this->db->escape(mb_strtolower($word));

            // Транслитерирана версия
            $transliteratedWord = $this->modelSearch->transliterate($word);
            $escapedTranslit = $this->db->escape(mb_strtolower($transliteratedWord));

            // Fuzzy варианти (за думи над 4 символа)
            $fuzzyVariants = $this->modelSearch->generateFuzzyVariants($word);

            // Търсене в различни полета
            $textFields = ['CONCAT(o.firstname, " ", o.lastname)', 'o.email', 'os.name', 'CONCAT(o.shipping_address_1, " ", o.shipping_city)'];

            foreach ($textFields as $field) {
                $wordConditions[] = "LOWER({$field}) LIKE '%{$escapedWord}%'";

                if ($transliteratedWord !== $word) {
                    $wordConditions[] = "LOWER({$field}) LIKE '%{$escapedTranslit}%'";
                }

                // Добавяме fuzzy варианти
                foreach ($fuzzyVariants as $variant) {
                    $escapedVariant = $this->db->escape(mb_strtolower($variant));
                    $wordConditions[] = "LOWER({$field}) LIKE '%{$escapedVariant}%'";
                }
            }

            // Търсене в номер на поръчка и телефон (само за числови стойности)
            if (is_numeric($word)) {
                $wordConditions[] = "o.order_id LIKE '%{$escapedWord}%'";
                $wordConditions[] = "o.telephone LIKE '%{$escapedWord}%'";
            }

            // Обединяваме условията за тази дума с OR
            $conditions[] = '(' . implode(' OR ', $wordConditions) . ')';
        }

        // Обединяваме всички думи с AND (всички думи трябва да се намират)
        return implode(' AND ', $conditions);
    }


    /**
     * Получава статус класа за визуализация
     *
     * @param int $status_id ID на статуса
     * @return string CSS клас за статуса
     */
    private function getStatusClass($status_id) {
        $status_classes = [
            1 => 'status-new',      // Нова / Очаква обработка
            2 => 'status-processing', // В процес
            3 => 'status-sent', // Изпратена
            5 => 'status-completed',  // Завършена
            7 => 'status-cancelled',  // Отказана
            8 => 'status-rejected',  // Отхвърлена
            10 => 'status-failed',  // Провалена
            11 => 'status-returned-payment', // Върнато плащане
            12 => 'status-returned', // Върната
            15 => 'status-processed', // Обработена
            16 => 'status-reset', // Нулирана
            17 => 'status-card-payed', // Платена с карта
            18 => 'status-card-rejected', // Отхвърлено плащане с карта
            19 => 'status-delayed', // В изчакване за по-късна дата
        ];

        $class = $status_classes[$status_id] ?? 'status-unknown';
        return ' ' . $class; // Добавяме интервал в началото
    }
}
