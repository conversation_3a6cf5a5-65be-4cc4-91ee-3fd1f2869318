<?php

namespace Theme25\Backend\Controller\Common;

class GlobalSearch extends \Theme25\Controller {

    public $modelSearch;
    public $languages = [];
    public $active_language_id = null;

    public function __construct($registry) {
        parent::__construct($registry, 'common/globalsearch');
        $this->loadModelAs('common/globalsearch', 'modelSearch');
        $this->_prepareLanguageData();
        $this->modelSearch->setLanguageData($this->languages, $this->active_language_id);
    }
    

    /**
     * Главен endpoint за глобалното търсене - показва HTML страница с общи резултати
     */
    public function index() {

        $this->setTitle('Глобално търсене');

        $this->initAdminData();

        $query = $this->requestGet('query');
        $extendedSearch = (int)$this->requestGet('extended_search', 0);

        if (!$query || strlen(trim($query)) < 2) {
            $this->renderIndexPage($query, [], 'Търсеният израз трябва да бъде поне 2 символа');
            return;
        }

        // Получаваме резултати от всички групи (ограничени до 5 за всяка група)
        $searchResults = $this->performSearch($query, 5, $extendedSearch, 1); // 5 = ограничаваме до 5 резултата, страница 1

        $limitedResults = [];
        $groupCounts = [];

        foreach ($searchResults as $type => $data) {
            $limitedResults[$type] = $data['results'] ?? [];
            $groupCounts[$type] = $data['total'] ?? 0;
        }

        $this->renderIndexPage($query, $limitedResults, null, $groupCounts);
    }

    /**
     * AJAX endpoint за глобалното търсене - връща JSON резултати
     */
    public function search() {
        $json = [];

        ob_start();

        $query = $this->requestGet('query');
        $extendedSearch = (int)$this->requestGet('extended_search', 0);

        $this->logDev("GlobalSearch::search - Query: " . $query . ", Extended: " . $extendedSearch);

        if (!$query || strlen(trim($query)) < 2) {
            $json['error'] = 'Търсеният израз трябва да бъде поне 2 символа';
        } else {
            // Получаваме ограничените резултати за dropdown
            $searchResults = $this->performSearch($query, 5, $extendedSearch, 1); // 5 = ограничаваме до 5 резултата, страница 1

            $limitedResults = [];
            $groupCounts = [];

            foreach ($searchResults as $type => $data) {
                $limitedResults[$type] = $data['results'] ?? [];
                $groupCounts[$type] = $data['total'] ?? 0;
            }

            // Добавяме броячите към резултатите
            $json = $limitedResults;
            $json['group_counts'] = $groupCounts;
        }

        $output = ob_get_clean();
        if ($output) {
            $json['error'] = $output;
        }

        $this->logDev("GlobalSearch::search - Final results: " . print_r($json, true));

        $this->setJSONResponseOutput($json);
    }

    /**
     * Извършва търсенето и връща резултатите
     */
    private function performSearch($query, $limit = 20, $extendedSearch = 0, $page = 1) {
        // Проверяваме дали търсенето е число за интелигентно приоритизиране
        $isNumericSearch = is_numeric(trim($query));

        // Зареждане на под-контролерите за различните типове търсене
        $results = [];

        // При числово търсене приоритизираме поръчките
        if ($isNumericSearch) {
            // Първо търсим в поръчки (най-висок приоритет за числа)
            $orderController = $this->setBackendSubController('Common/GlobalSearch/Orders', $this);
            if ($orderController) {
                $orderData = $orderController->search($query, $page, $limit, $extendedSearch);
                $results['orders'] = $orderData;
            }

            // Ако намерим поръчка, търсим клиента който я е направил
            if (!empty($results['orders']['results'])) {
                // Вземаме първата поръчка и търсим нейния клиент
                $firstOrder = $results['orders']['results'][0];
                if (!empty($firstOrder['email'])) {
                    $customerController = $this->setBackendSubController('Common/GlobalSearch/Customers', $this);
                    if ($customerController) {
                        $customerData = $customerController->search($firstOrder['email'], $page, $limit, $extendedSearch);
                        $results['customers'] = $customerData;
                    }
                }
            } else {
                // Ако няма поръчки, търсим в продукти
                $productController = $this->setBackendSubController('Common/GlobalSearch/Products', $this);
                if ($productController) {
                    $productData = $productController->search($query, $page, $limit, $extendedSearch);
                    $results['products'] = $productData;
                }
            }
        } else {
            // При не-числово търсене използваме стандартния ред
            // Търсене в продукти
            $productController = $this->setBackendSubController('Common/GlobalSearch/Products', $this);

            $this->logDev("GlobalSearch::performSearch - Product controller: " . get_class($productController));

            if ($productController) {
                $productData = $productController->search($query, $page, $limit, $extendedSearch);
                $results['products'] = $productData;

                $this->logDev("GlobalSearch::performSearch - Product results: " . print_r($productData, true));
            }

            // Търсене в категории
            $categoryController = $this->setBackendSubController('Common/GlobalSearch/Categories', $this);
            if ($categoryController) {
                $categoryData = $categoryController->search($query, $page, $limit, $extendedSearch);
                $results['categories'] = $categoryData;
            }

            // Търсене в поръчки (само ако не е числово търсене)
            $orderController = $this->setBackendSubController('Common/GlobalSearch/Orders', $this);
            if ($orderController) {
                $orderData = $orderController->search($query, $page, $limit, $extendedSearch);
                $results['orders'] = $orderData;
            }

            // Търсене в клиенти
            $customerController = $this->setBackendSubController('Common/GlobalSearch/Customers', $this);
            if ($customerController) {
                $customerData = $customerController->search($query, $page, $limit, $extendedSearch);
                $results['customers'] = $customerData;
            }
        }

        return $results;
    }

    /**
     * Рендериране на index страницата с картите и ограничени резултати
     */
    private function renderIndexPage($query, $results, $error = null, $groupCounts = null) {
        $this->setData([
            'user_token' => $this->session->data['user_token'],
            'query' => $query,
            'error' => $error,
            'title' => 'Глобално търсене'
        ]);

        // Групови броячи за картите
        if ($groupCounts !== null) {
            $this->setData(['group_counts' => $groupCounts]);
        } else {
            $this->setData(['group_counts' => []]);
        }

        // Рендираме резултатите от всяка група чрез съответните субконтролери
        $renderedSections = [];

        if (!empty($results)) {
            foreach ($results as $type => $typeResults) {
                if (!empty($typeResults)) {
                    $renderedSections[$type] = $this->renderGroupSection($type, $typeResults, false, $query, 1, 5, count($typeResults)); // false = без пагинация
                }
            }
        }

        $this->setData(['rendered_sections' => $renderedSections]);

        $this->renderTemplateWithDataAndOutput('common/global_search_index');
    }

    /**
     * Рендериране на секция за определена група резултати
     */
    private function renderGroupSection($type, $results, $withPagination = true, $query = '', $page = 1, $limit = 20, $total = 0) {
        $subControllerMap = [
            'products' => 'Common/GlobalSearch/Products',
            'orders' => 'Common/GlobalSearch/Orders',
            'customers' => 'Common/GlobalSearch/Customers',
            'categories' => 'Common/GlobalSearch/Categories'
        ];

        if (!isset($subControllerMap[$type])) {
            return '';
        }

        $subController = $this->setBackendSubController($subControllerMap[$type], $this);
        if (!$subController) {
            return '';
        }

        // Извикваме renderItems метода от субконтролера
        return $subController->renderItems($results, $withPagination, $query, $page, $limit, $total);
    }

    /**
     * Търсене във всички продукти
     */
    public function products() {

        $this->setTitle('Търсене във всички продукти');

        $this->initAdminData();

        $query = $this->requestGet('query');
        $extendedSearch = (int)$this->requestGet('extended_search', 0);
        $page = (int)$this->requestGet('page', 1);
        $limit = (int)$this->requestGet('limit', 20);

        if (!$query || strlen(trim($query)) < 2) {
            $this->renderGroupPage('products', $query, [], 0, $page, $limit, 'Търсеният израз трябва да бъде поне 2 символа');
            return;
        }

        $productController = $this->setBackendSubController('Common/GlobalSearch/Products', $this);
        if ($productController) {
            $data = $productController->search($query, $page, $limit, $extendedSearch);
            $this->renderGroupPage('products', $query, $data['results'] ?? [], $data['total'] ?? 0, $page, $limit);
        } else {
            $this->renderGroupPage('products', $query, [], 0, $page, $limit, 'Грешка при зареждане на контролера за продукти');
        }
    }

    /**
     * Търсене във всички категории
     */
     public function categories() {

        $this->setTitle('Търсене във всички категории');

        $this->initAdminData();

        $query = $this->requestGet('query');
        $extendedSearch = (int)$this->requestGet('extended_search', 0);
        $page = (int)$this->requestGet('page', 1);
        $limit = (int)$this->requestGet('limit', 20);

        if (!$query || strlen(trim($query)) < 2) {
            $this->renderGroupPage('categories', $query, [], 0, $page, $limit, 'Търсеният израз трябва да бъде поне 2 символа');
            return;
        }

        $categoryController = $this->setBackendSubController('Common/GlobalSearch/Categories', $this);
        if ($categoryController) {
            $data = $categoryController->search($query, $page, $limit, $extendedSearch);
            $this->renderGroupPage('categories', $query, $data['results'] ?? [], $data['total'] ?? 0, $page, $limit);
        } else {
            $this->renderGroupPage('categories', $query, [], 0, $page, $limit, 'Грешка при зареждане на контролера за категории');
        }
    }

    /**
     * Търсене във всички поръчки
     */
     public function orders() {

        $this->setTitle('Търсене във всички поръчки');

        $this->initAdminData();

        $query = $this->requestGet('query');
        $extendedSearch = (int)$this->requestGet('extended_search', 0);
        $page = (int)$this->requestGet('page', 1);
        $limit = (int)$this->requestGet('limit', 20);

        if (!$query || strlen(trim($query)) < 2) {
            $this->renderGroupPage('orders', $query, [], 0, $page, $limit, 'Търсеният израз трябва да бъде поне 2 символа');
            return;
        }

        $orderController = $this->setBackendSubController('Common/GlobalSearch/Orders', $this);
        if ($orderController) {
            $data = $orderController->search($query, $page, $limit, $extendedSearch);
            $this->renderGroupPage('orders', $query, $data['results'] ?? [], $data['total'] ?? 0, $page, $limit);
        } else {
            $this->renderGroupPage('orders', $query, [], 0, $page, $limit, 'Грешка при зареждане на контролера за поръчки');
        }
    }

    /**
     * Търсене във всички клиенти
     */
    public function customers() {

        $this->setTitle('Търсене във всички клиенти');

        $this->initAdminData();

        $query = $this->requestGet('query');
        $extendedSearch = (int)$this->requestGet('extended_search', 0);
        $page = (int)$this->requestGet('page', 1);
        $limit = (int)$this->requestGet('limit', 20);

        if (!$query || strlen(trim($query)) < 2) {
            $this->renderGroupPage('customers', $query, [], 0, $page, $limit, 'Търсеният израз трябва да бъде поне 2 символа');
            return;
        }

        $customerController = $this->setBackendSubController('Common/GlobalSearch/Customers', $this);
        if ($customerController) {
            $data = $customerController->search($query, $page, $limit, $extendedSearch);
            $this->renderGroupPage('customers', $query, $data['results'] ?? [], $data['total'] ?? 0, $page, $limit);
        } else {
            $this->renderGroupPage('customers', $query, [], 0, $page, $limit, 'Грешка при зареждане на контролера за клиенти');
        }
    }



    /**
     * Рендериране на страница за определена група резултати
     */
    private function renderGroupPage($type, $query, $results, $total, $page, $limit, $error = null) {
        $this->setData([
            'user_token' => $this->session->data['user_token'],
            'type' => $type,
            'query' => $query,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'error' => $error
        ]);

        // Заглавия за различните типове
        $titles = [
            'products' => 'Продукти',
            'categories' => 'Категории',
            'orders' => 'Поръчки',
            'customers' => 'Клиенти'
        ];

        $this->setData(['title' => $titles[$type] ?? 'Резултати']);

        // Рендираме резултатите чрез съответния субконтролер
        $renderedContent = '';
        if (!empty($results) && !$error) {
            $renderedContent = $this->renderGroupSection($type, $results, true, $query, $page, $limit, $total);
        }

        $this->setData(['rendered_content' => $renderedContent]);

        // Пагинация с използване на Theme25\Pagination класа
        if ($total > $limit) {
            $pagination = new \Theme25\Pagination();
            $pagination->total = $total;
            $pagination->page = $page;
            $pagination->limit = $limit;
            $pagination->url = $this->getAdminLink('common/globalsearch/' . $type, '&query=' . urlencode($query) . '&page={page}');

            // Задаваме подходящ текст според типа
            $productTexts = [
                'products' => 'продукта',
                'categories' => 'категории',
                'orders' => 'поръчки',
                'customers' => 'клиента'
            ];
            $pagination->setProductText($productTexts[$type] ?? 'резултата');

            $this->setData(['pagination' => $pagination->render() ]);
        }
        $this->renderTemplateWithDataAndOutput('common/global_search_group');
    }

     /**
     * Подготвя данните за езиците.
     */
    private function _prepareLanguageData() {
        $this->prepareLanguageData();

        $this->setData([
            'languages' => $this->languages,
            'active_language_id' => $this->active_language_id
        ]);
    }

    /**
     * Изчиства кеша за търсенето
     */
    public function clearCache() {
        $json = ['success' => false];

        try {
            // Изчистваме кеша за всички под-контролери
               $this->modelSearch->clearCache();
            
            $json['success'] = true;
            $json['message'] = 'Кешът за търсенето е изчистен успешно';

        } catch (Exception $e) {
            $json['error'] = 'Грешка при изчистване на кеша: ' . $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    

}
