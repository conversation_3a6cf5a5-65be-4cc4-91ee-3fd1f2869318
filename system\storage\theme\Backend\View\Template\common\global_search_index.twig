{{ header }}{{ column_left }}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div class="container-fluid">
                    <div class="float-right">
                        <a href="{{ back_url }}" data-toggle="tooltip" title="Назад" class="btn btn-light">
                            <i class="ri-arrow-left-line"></i>
                        </a>
                    </div>
                    <h1>{{ title }}</h1>
                    <ul class="breadcrumb">
                        <li><a href="{{ home }}">Начало</a></li>
                        <li>{{ title }}</li>
                    </ul>
                </div>
            </div>

            <div class="container-fluid">
                {% if error %}
                    <div class="alert alert-danger alert-dismissible">
                        <i class="ri-error-warning-line"></i> {{ error }}
                        <button type="button" class="close" data-dismiss="alert">&times;</button>
                    </div>
                {% endif %}

                {% if query %}
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <i class="ri-search-line"></i> Резултати за: "{{ query }}"
                            </h3>
                        </div>
                        <div class="card-body">
                            <!-- Карти с групите търсения -->
                            <div class="row mb-4">
                                {% if group_counts.orders > 0 %}
                                    <div class="col-md-3 mb-3">
                                        <div class="card bg-primary text-white">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <h4 class="card-title">{{ group_counts.orders }}</h4>
                                                        <p class="card-text">Поръчки</p>
                                                    </div>
                                                    <div class="align-self-center">
                                                        <i class="ri-shopping-cart-line fa-2x"></i>
                                                    </div>
                                                </div>
                                                <a href="{{ url('common/globalsearch/orders', 'user_token=' ~ user_token ~ '&query=' ~ query|url_encode) }}" 
                                                   class="btn btn-light btn-sm mt-2">
                                                    Виж всички {{ group_counts.orders }} резултата
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                {% endif %}

                                {% if group_counts.customers > 0 %}
                                    <div class="col-md-3 mb-3">
                                        <div class="card bg-success text-white">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <h4 class="card-title">{{ group_counts.customers }}</h4>
                                                        <p class="card-text">Клиенти</p>
                                                    </div>
                                                    <div class="align-self-center">
                                                        <i class="ri-user-line fa-2x"></i>
                                                    </div>
                                                </div>
                                                <a href="{{ url('common/globalsearch/customers', 'user_token=' ~ user_token ~ '&query=' ~ query|url_encode) }}" 
                                                   class="btn btn-light btn-sm mt-2">
                                                    Виж всички {{ group_counts.customers }} резултата
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                {% endif %}

                                {% if group_counts.products > 0 %}
                                    <div class="col-md-3 mb-3">
                                        <div class="card bg-info text-white">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <h4 class="card-title">{{ group_counts.products }}</h4>
                                                        <p class="card-text">Продукти</p>
                                                    </div>
                                                    <div class="align-self-center">
                                                        <i class="ri-product-hunt-line fa-2x"></i>
                                                    </div>
                                                </div>
                                                <a href="{{ url('common/globalsearch/products', 'user_token=' ~ user_token ~ '&query=' ~ query|url_encode) }}" 
                                                   class="btn btn-light btn-sm mt-2">
                                                    Виж всички {{ group_counts.products }} резултата
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                {% endif %}

                                {% if group_counts.categories > 0 %}
                                    <div class="col-md-3 mb-3">
                                        <div class="card bg-warning text-white">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <h4 class="card-title">{{ group_counts.categories }}</h4>
                                                        <p class="card-text">Категории</p>
                                                    </div>
                                                    <div class="align-self-center">
                                                        <i class="ri-folder-line fa-2x"></i>
                                                    </div>
                                                </div>
                                                <a href="{{ url('common/globalsearch/categories', 'user_token=' ~ user_token ~ '&query=' ~ query|url_encode) }}" 
                                                   class="btn btn-light btn-sm mt-2">
                                                    Виж всички {{ group_counts.categories }} резултата
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Секции с резултати -->
                            {% if rendered_sections %}
                                {% for type, content in rendered_sections %}
                                    {% if content %}
                                        <div class="mb-4">
                                            <h4 class="mb-3">
                                                {% if type == 'orders' %}
                                                    <i class="ri-shopping-cart-line"></i> Поръчки
                                                {% elseif type == 'customers' %}
                                                    <i class="ri-user-line"></i> Клиенти
                                                {% elseif type == 'products' %}
                                                    <i class="ri-product-hunt-line"></i> Продукти
                                                {% elseif type == 'categories' %}
                                                    <i class="ri-folder-line"></i> Категории
                                                {% endif %}
                                                (до 5 резултата)
                                            </h4>
                                            {{ content|raw }}
                                            
                                            {% if group_counts[type] > 5 %}
                                                <div class="text-center mt-3">
                                                    <a href="{{ url('common/globalsearch/' ~ type, 'user_token=' ~ user_token ~ '&query=' ~ query|url_encode) }}" 
                                                       class="btn btn-primary">
                                                        Виж всички {{ group_counts[type] }} резултата
                                                    </a>
                                                </div>
                                            {% endif %}
                                        </div>
                                    {% endif %}
                                {% endfor %}
                            {% else %}
                                <div class="text-center py-5">
                                    <i class="ri-search-line fa-3x text-muted mb-3"></i>
                                    <h4 class="text-muted">Няма намерени резултати</h4>
                                    <p class="text-muted">Опитайте с различни ключови думи</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% else %}
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="ri-search-line fa-3x text-muted mb-3"></i>
                            <h4 class="text-muted">Глобално търсене</h4>
                            <p class="text-muted">Използвайте полето за търсене в горната част на страницата</p>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{{ footer }}
