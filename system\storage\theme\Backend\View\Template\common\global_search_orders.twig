{% if orders or customers %}
    {% if orders %}
    <div class="overflow-x-auto">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Поръчки</h3>
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <div class="flex items-center">
                            <input type="checkbox" class="mr-2">
                            Номер
                        </div>
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Дата
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Клиент
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Начин на плащане
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Статус
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Сума
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Действия
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for order in orders %}
                <tr class="hover:bg-gray-50" data-order-id="{{ order.order_id }}">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <input type="checkbox" name="selected[]" value="{{ order.order_id }}" class="mr-2">
                            <span class="text-sm font-medium text-gray-900">#{{ order.order_id }}</span>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ order.date_added }}</div>
                        {% if order.date_modified %}
                        <div class="text-sm text-gray-500">Изм: {{ order.date_modified }}</div>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4">
                        <div class="text-sm font-medium text-gray-900">{{ order.customer_name }}</div>
                        {% if order.email %}
                        <div class="text-sm text-gray-500">{{ order.email }}</div>
                        {% endif %}
                        {% if order.telephone %}
                        <div class="text-sm text-gray-500">{{ order.telephone }}</div>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ order.payment_method|default('N/A') }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ order.status_class }}">
                            {{ order.status }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">{{ order.total }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex items-center justify-end space-x-2">
                            <a href="{{ order.view }}" class="text-blue-600 hover:text-blue-900" title="Преглед">
                                <i class="ri-eye-line"></i>
                            </a>
                            <a href="{{ order.edit }}" class="text-primary hover:text-primary/80" title="Редактирай">
                                <i class="ri-edit-line"></i>
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}

    {% if customers %}
    <div class="overflow-x-auto {% if orders %}mt-8{% endif %}">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Свързани клиенти</h3>
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        ID
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Име
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Email
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Телефон
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Статус
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Дата
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Действия
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for customer in customers %}
                <tr class="hover:bg-gray-50" data-customer-id="{{ customer.customer_id }}">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="text-sm font-medium text-gray-900">#{{ customer.customer_id }}</span>
                    </td>
                    <td class="px-6 py-4">
                        <div class="text-sm font-medium text-gray-900">{{ customer.customer_name }}</div>
                        {% if customer.customer_group %}
                        <div class="text-sm text-gray-500">{{ customer.customer_group }}</div>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4">
                        <div class="text-sm text-gray-900">{{ customer.email }}</div>
                    </td>
                    <td class="px-6 py-4">
                        <div class="text-sm text-gray-900">{{ customer.telephone }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if customer.status == 'Активен' %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                            {{ customer.status }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ customer.date_added }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex items-center justify-end space-x-2">
                            <a href="{{ customer.view }}" class="text-blue-600 hover:text-blue-900" title="Преглед">
                                <i class="ri-eye-line"></i>
                            </a>
                            <a href="{{ customer.edit }}" class="text-primary hover:text-primary/80" title="Редактирай">
                                <i class="ri-edit-line"></i>
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}
{% else %}
    <div class="text-center py-8">
        <i class="ri-shopping-cart-line text-gray-400 text-4xl mb-4"></i>
        <p class="text-gray-500">Няма намерени поръчки</p>
    </div>
{% endif %}
