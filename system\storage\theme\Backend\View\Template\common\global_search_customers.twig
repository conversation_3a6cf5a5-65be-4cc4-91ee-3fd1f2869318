{% if customers %}
    <div class="table-responsive">
        <table class="table table-bordered table-hover">
            <thead>
                <tr>
                    <td class="text-left">ID</td>
                    <td class="text-left">Име</td>
                    <td class="text-left">Email</td>
                    <td class="text-left">Телефон</td>
                    <td class="text-center">Статус</td>
                    <td class="text-left">Дата на регистрация</td>
                    <td class="text-right">Действия</td>
                </tr>
            </thead>
            <tbody>
                {% for customer in customers %}
                    <tr>
                        <td class="text-left">
                            <a href="{{ customer.view }}" class="text-decoration-none">
                                #{{ customer.customer_id }}
                            </a>
                        </td>
                        <td class="text-left">
                            <a href="{{ customer.view }}" class="text-decoration-none">
                                {{ customer.name }}
                            </a>
                        </td>
                        <td class="text-left">
                            {% if customer.email %}
                                <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                    {{ customer.email }}
                                </a>
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="text-left">
                            {% if customer.telephone %}
                                <a href="tel:{{ customer.telephone }}" class="text-decoration-none">
                                    {{ customer.telephone }}
                                </a>
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <span class="badge {{ customer.status_class }}">
                                {{ customer.status }}
                            </span>
                        </td>
                        <td class="text-left">
                            {% if customer.date_added %}
                                {{ customer.date_added|date('d.m.Y H:i') }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="text-right">
                            <div class="btn-group">
                                <a href="{{ customer.view }}" 
                                   data-toggle="tooltip" title="Преглед" 
                                   class="btn btn-info btn-sm">
                                    <i class="ri-eye-line"></i>
                                </a>
                                <a href="{{ customer.edit }}" 
                                   data-toggle="tooltip" title="Редактиране" 
                                   class="btn btn-primary btn-sm">
                                    <i class="ri-edit-line"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
{% else %}
    <div class="text-center py-4">
        <i class="ri-user-line fa-2x text-muted mb-2"></i>
        <p class="text-muted">Няма намерени клиенти</p>
    </div>
{% endif %}
