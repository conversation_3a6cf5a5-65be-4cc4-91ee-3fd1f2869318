
<header class="bg-white border-b border-gray-200">
<div class="flex items-center justify-between p-4">
<div class="flex items-center">
<button id="mobile-menu" class="mr-4 md:hidden text-gray-500 hover:text-primary">
<div class="w-8 h-8 flex items-center justify-center">
<i class="ri-menu-line ri-lg"></i>
</div>
</button>
</div>
<div class="flex-1 mx-8" style="max-width: 50rem;">
<div class="relative flex">
<div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
<div class="w-5 h-5 flex items-center justify-center text-gray-400">
<i class="ri-search-line"></i>
</div>
</div>
<input type="text" class="search-input w-full pl-10 pr-4 py-2 border border-gray-300 rounded-l-button focus:outline-none focus:border-primary" placeholder="Търсене на продукти, поръчки, клиенти..." value="{% if search_query %}{{ search_query }}{% endif %}">
<button class="px-4 py-2 bg-primary text-white rounded-r-button hover:bg-primary/90 transition-colors whitespace-nowrap">
<div class="w-5 h-5 flex items-center justify-center">
<i class="ri-search-line"></i>
</div>
</button>
</div>
</div>
<div class="flex items-center space-x-4">
<a href="{{ home }}" target="_blank" class="relative text-gray-600 hover:text-primary">
<div class="w-10 h-10 flex items-center justify-center">
<i class="ri-external-link-line ri-lg"></i>
</div>
</a>
<button class="relative text-gray-600 hover:text-primary">
<div class="w-10 h-10 flex items-center justify-center">
<i class="ri-notification-3-line ri-lg"></i>
</div>
{% if notifications %}
<span class="absolute top-2 right-2 w-4 h-4 bg-red-500 rounded-full text-white text-xs flex items-center justify-center">{{ notifications }}</span>
{% endif %}
</button>
<div class="relative">
<button id="profile-menu-button" class="flex items-center">
<div class="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-600">

<i class="ri-user-line ri-lg"></i>

</div>
</button>
</div>
<a href="{{ logout }}" class="flex items-center px-4 py-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-button transition-colors whitespace-nowrap">
<div class="w-5 h-5 flex items-center justify-center mr-2">
<i class="ri-logout-box-line"></i>
</div>
<span>Изход</span>
</a>
</div>
</div>
<div class="flex justify-between border-b border-gray-200 pr-5">
<div class="flex">
{% if tabs %}
 {% for tab in tabs %}
<button class="px-6 py-3 text-gray-600 hover:text-primary whitespace-nowrap {% if tab.active %}text-primary border-b-2 border-primary font-medium{% endif %}">{{ tab.name }}</button>
 {% endfor %}
{% endif %}
</div>
<button class="px-6 py-3 text-gray-600 hover:text-primary hover:bg-red-50 rounded-button transition-colors whitespace-nowrap flex items-center mb-1">
<div class="w-5 h-5 flex items-center justify-center mr-2">
<i class="ri-refresh-line"></i>
</div>
Кеш
</button>
</div>
</header>