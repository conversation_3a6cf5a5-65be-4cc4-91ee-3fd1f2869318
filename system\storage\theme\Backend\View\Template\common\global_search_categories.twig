{% if categories %}
    <div class="table-responsive">
        <table class="table table-bordered table-hover">
            <thead>
                <tr>
                    <td class="text-left">ID</td>
                    <td class="text-left">Име на категория</td>
                    <td class="text-center">Статус</td>
                    <td class="text-center">Подредба</td>
                    <td class="text-left">Дата на добавяне</td>
                    <td class="text-right">Действия</td>
                </tr>
            </thead>
            <tbody>
                {% for category in categories %}
                    <tr>
                        <td class="text-left">
                            <a href="{{ category.view }}" class="text-decoration-none">
                                #{{ category.category_id }}
                            </a>
                        </td>
                        <td class="text-left">
                            <a href="{{ category.view }}" class="text-decoration-none">
                                {{ category.name }}
                            </a>
                            {% if category.parent_id > 0 %}
                                <small class="text-muted">(Подкатегория)</small>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <span class="badge {{ category.status_class }}">
                                {{ category.status }}
                            </span>
                        </td>
                        <td class="text-center">{{ category.sort_order }}</td>
                        <td class="text-left">
                            {% if category.date_added %}
                                {{ category.date_added|date('d.m.Y H:i') }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="text-right">
                            <div class="btn-group">
                                <a href="{{ category.view }}" 
                                   data-toggle="tooltip" title="Преглед" 
                                   class="btn btn-info btn-sm">
                                    <i class="ri-eye-line"></i>
                                </a>
                                <a href="{{ category.edit }}" 
                                   data-toggle="tooltip" title="Редактиране" 
                                   class="btn btn-primary btn-sm">
                                    <i class="ri-edit-line"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
{% else %}
    <div class="text-center py-4">
        <i class="ri-folder-line fa-2x text-muted mb-2"></i>
        <p class="text-muted">Няма намерени категории</p>
    </div>
{% endif %}
