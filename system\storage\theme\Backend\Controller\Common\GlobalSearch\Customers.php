<?php

namespace Theme25\Backend\Controller\Common\GlobalSearch;

class Customers extends \Theme25\ControllerSubMethods {

    /**
     * Кеш директория за търсенето
     */
    // private $cacheDir = 'system/storage/cache/search/';

    /**
     * Време на валидност на кеша в секунди (1 час)
     */
    private $cacheExpiry = 3600;

    /**
     * Транслитерационна таблица кирилица -> латиница
     */
    private $translitMap = [
        'а' => 'a', 'б' => 'b', 'в' => 'v', 'г' => 'g', 'д' => 'd', 'е' => 'e',
        'ж' => 'zh', 'з' => 'z', 'и' => 'i', 'й' => 'y', 'к' => 'k', 'л' => 'l',
        'м' => 'm', 'н' => 'n', 'о' => 'o', 'п' => 'p', 'р' => 'r', 'с' => 's',
        'т' => 't', 'у' => 'u', 'ф' => 'f', 'х' => 'h', 'ц' => 'c', 'ч' => 'ch',
        'ш' => 'sh', 'щ' => 'sht', 'ъ' => 'a', 'ь' => 'y', 'ю' => 'yu', 'я' => 'ya'
    ];

    public function __construct($registry) {
        parent::__construct($registry);
    }

    /**
     * Търсене в клиенти (за dropdown) с кеширане
     */
    public function search($query) {
        // Проверяваме кеша първо
        $cacheKey = $this->generateCacheKey('customers_dropdown', $query, 5);
        $cachedResults = $this->getCachedResults($cacheKey);

        if ($cachedResults !== null) {
            return $cachedResults;
        }

        // Ако няма кеширани резултати, извършваме търсенето
        $results = $this->performOptimizedSearch($query, 5);

        // Кешираме резултатите
        $this->cacheResults($cacheKey, $results);

        return $results;
    }

    /**
     * Генерира ключ за кеша
     */
    private function generateCacheKey($type, $query, $limit = null, $offset = null) {
        $keyData = [
            'type' => $type,
            'query' => mb_strtolower(trim($query)),
            'limit' => $limit,
            'offset' => $offset
        ];
        return 'search_' . md5(serialize($keyData));
    }

    /**
     * Получава кеширани резултати
     */
    private function getCachedResults($cacheKey) {
        $cacheFile = $this->cacheDir . $cacheKey . '.cache';

        if (!file_exists($cacheFile)) {
            return null;
        }

        $cacheData = file_get_contents($cacheFile);
        if ($cacheData === false) {
            return null;
        }

        $data = unserialize($cacheData);
        if ($data === false || !isset($data['timestamp']) || !isset($data['results'])) {
            return null;
        }

        // Проверяваме дали кешът е изтекъл
        if (time() - $data['timestamp'] > $this->cacheExpiry) {
            unlink($cacheFile);
            return null;
        }

        return $data['results'];
    }

    /**
     * Кешира резултатите
     */
    private function cacheResults($cacheKey, $results) {
        // Създаваме кеш директорията ако не съществува
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }

        $cacheFile = $this->cacheDir . $cacheKey . '.cache';
        $cacheData = [
            'timestamp' => time(),
            'results' => $results
        ];

        file_put_contents($cacheFile, serialize($cacheData));
    }

    /**
     * Изчиства кеша за клиенти
     */
    public function clearCache() {
        if (is_dir($this->cacheDir)) {
            $files = glob($this->cacheDir . 'search_*.cache');
            foreach ($files as $file) {
                if (strpos($file, 'customers') !== false) {
                    unlink($file);
                }
            }
        }
    }

    /**
     * Оптимизирано търсене с релевантност
     */
    private function performOptimizedSearch($query, $limit = 5, $offset = 0) {
        try {
            // Подготвяме думите за търсене
            $words = $this->prepareSearchWords($query);
            if (empty($words)) {
                return [];
            }

            // Търсим точни съвпадения първо
            $exactResults = $this->searchExactMatches($words, $limit);

            // Ако имаме достатъчно точни резултати, връщаме ги
            if (count($exactResults) >= $limit) {
                return array_slice($exactResults, $offset, $limit);
            }

            // Търсим частични съвпадения за останалите места
            $remainingLimit = $limit - count($exactResults);
            $partialResults = $this->searchPartialMatches($words, $remainingLimit, $exactResults);

            // Обединяваме резултатите
            $allResults = array_merge($exactResults, $partialResults);

            return array_slice($allResults, $offset, $limit);

        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * Търси точни съвпадения в клиенти с интелигентна логика
     */
    private function searchExactMatches($words, $limit) {
        $results = [];
        $language_id = $this->getLanguageId();

        // Проверяваме дали търсенето е email адрес
        if (count($words) == 1 && filter_var($words[0], FILTER_VALIDATE_EMAIL)) {
            $escapedEmail = $this->db->escape(mb_strtolower($words[0]));

            $sql = "
                SELECT DISTINCT
                    c.customer_id,
                    c.firstname,
                    c.lastname,
                    c.email,
                    c.telephone,
                    c.status,
                    c.date_added,
                    cg.name as customer_group,
                    'exact' as match_type
                FROM
                    " . DB_PREFIX . "customer c
                LEFT JOIN
                    " . DB_PREFIX . "customer_group_description cg ON (c.customer_group_id = cg.customer_group_id)
                WHERE
                    LOWER(c.email) = '" . $escapedEmail . "'
                    AND cg.language_id = '" . (int)$language_id . "'
                    AND (c.firstname IS NOT NULL AND c.firstname != ''
                         OR c.lastname IS NOT NULL AND c.lastname != '')
                ORDER BY
                    c.date_added DESC
                LIMIT " . (int)$limit;

            $query_result = $this->db->query($sql);

            foreach ($query_result->rows as $row) {
                $name = trim($row['firstname'] . ' ' . $row['lastname']);
                // Пропускаме записи с празни имена
                if (empty($name) || $name === ' ') {
                    continue;
                }

                $results[] = [
                    'customer_id' => $row['customer_id'],
                    'name' => $name,
                    'email' => $row['email'] ?? '',
                    'telephone' => $row['telephone'] ?? '',
                    'status' => $row['status'] ? 'Активен' : 'Неактивен',
                    'customer_group' => $row['customer_group'] ?? '',
                    'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                    'relevance' => 100
                ];
            }
            // Ако намерим точно съвпадение по email, връщаме САМО този клиент
            return $results;
        }

        // Проверяваме дали търсенето е число (за order_id търсене)
        if (count($words) == 1 && is_numeric($words[0])) {
            $orderId = (int)$words[0];

            $this->logDev("Customers::searchExactMatches - Searching for customer by order_id: {$orderId}");

            // Търсим клиента който е направил поръчка с този номер
            $sql = "
                SELECT DISTINCT
                    c.customer_id,
                    c.firstname,
                    c.lastname,
                    c.email,
                    c.telephone,
                    c.status,
                    c.date_added,
                    cg.name as customer_group,
                    o.order_id,
                    'exact' as match_type
                FROM " . DB_PREFIX . "customer c
                LEFT JOIN " . DB_PREFIX . "customer_group_description cg ON (c.customer_group_id = cg.customer_group_id)
                INNER JOIN " . DB_PREFIX . "order o ON (c.customer_id = o.customer_id)
                WHERE cg.language_id = '" . (int)$language_id . "'
                AND o.order_id = '" . (int)$orderId . "'
                AND (c.firstname IS NOT NULL AND c.firstname != ''
                     OR c.lastname IS NOT NULL AND c.lastname != '')
                ORDER BY c.date_added DESC
                LIMIT " . (int)$limit;

            $query_result = $this->db->query($sql);

            $this->logDev("Customers::searchExactMatches - Order search SQL: " . $sql);
            $this->logDev("Customers::searchExactMatches - Order search results: " . print_r($query_result->rows, true));

            foreach ($query_result->rows as $row) {
                $name = trim($row['firstname'] . ' ' . $row['lastname']);
                // Пропускаме записи с празни имена
                if (empty($name) || $name === ' ') {
                    continue;
                }

                $results[] = [
                    'customer_id' => $row['customer_id'],
                    'name' => $name,
                    'email' => $row['email'] ?? '',
                    'telephone' => $row['telephone'] ?? '',
                    'status' => $row['status'] ? 'Активен' : 'Неактивен',
                    'customer_group' => $row['customer_group'] ?? '',
                    'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                    'relevance' => 100,
                    'found_via_order' => $row['order_id'] // Добавяме информация че е намерен чрез поръчка
                ];
            }

            // Ако намерихме клиент чрез поръчка, връщаме резултата
            if (!empty($results)) {
                $this->logDev("Customers::searchExactMatches - Found customer via order_id: " . print_r($results, true));
                return $results;
            }
        }

        // Проверяваме дали търсенето е телефонен номер (само числа или с +)
        if (count($words) == 1 && preg_match('/^[\+]?[0-9\s\-\(\)]+$/', $words[0])) {
            $phone = preg_replace('/[^\+0-9]/', '', $words[0]); // Премахваме всички символи освен + и цифри
            $escapedPhone = $this->db->escape($phone);

            $sql = "
                SELECT DISTINCT
                    c.customer_id,
                    c.firstname,
                    c.lastname,
                    c.email,
                    c.telephone,
                    c.status,
                    c.date_added,
                    cg.name as customer_group,
                    'exact' as match_type
                FROM
                    " . DB_PREFIX . "customer c
                LEFT JOIN
                    " . DB_PREFIX . "customer_group_description cg ON (c.customer_group_id = cg.customer_group_id)
                WHERE
                    REPLACE(REPLACE(REPLACE(REPLACE(c.telephone, ' ', ''), '-', ''), '(', ''), ')', '') = '" . $escapedPhone . "'
                    AND cg.language_id = '" . (int)$language_id . "'
                    AND (c.firstname IS NOT NULL AND c.firstname != ''
                         OR c.lastname IS NOT NULL AND c.lastname != '')
                ORDER BY
                    c.date_added DESC
                LIMIT " . (int)$limit;

            $query_result = $this->db->query($sql);

            foreach ($query_result->rows as $row) {
                $name = trim($row['firstname'] . ' ' . $row['lastname']);
                // Пропускаме записи с празни имена
                if (empty($name) || $name === ' ') {
                    continue;
                }

                $results[] = [
                    'customer_id' => $row['customer_id'],
                    'name' => $name,
                    'email' => $row['email'] ?? '',
                    'telephone' => $row['telephone'] ?? '',
                    'status' => $row['status'] ? 'Активен' : 'Неактивен',
                    'customer_group' => $row['customer_group'] ?? '',
                    'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                    'relevance' => 100
                ];
            }
            // Ако намерим точно съвпадение по телефон, връщаме САМО този клиент
            return $results;
        }

        return $results;
    }

    /**
     * Търси частични съвпадения в клиенти
     */
    private function searchPartialMatches($words, $limit, $excludeResults = []) {
        $results = [];
        $excludeIds = array_column($excludeResults, 'customer_id');
        $language_id = $this->getLanguageId();

        // Проверяваме дали търсенето е число (за order_id търсене) - само ако няма точни резултати
        if (count($words) == 1 && is_numeric($words[0]) && empty($excludeResults)) {
            $orderId = (int)$words[0];

            $this->logDev("Customers::searchPartialMatches - Searching for customer by order_id: {$orderId}");

            // Търсим клиента който е направил поръчка с този номер
            $sql = "
                SELECT DISTINCT
                    c.customer_id,
                    c.firstname,
                    c.lastname,
                    c.email,
                    c.telephone,
                    c.status,
                    c.date_added,
                    cg.name as customer_group,
                    o.order_id,
                    'partial' as match_type
                FROM " . DB_PREFIX . "customer c
                LEFT JOIN " . DB_PREFIX . "customer_group_description cg ON (c.customer_group_id = cg.customer_group_id)
                INNER JOIN " . DB_PREFIX . "order o ON (c.customer_id = o.customer_id)
                WHERE cg.language_id = '" . (int)$language_id . "'
                AND o.order_id = '" . (int)$orderId . "'
                AND (c.firstname IS NOT NULL AND c.firstname != ''
                     OR c.lastname IS NOT NULL AND c.lastname != '')
                ORDER BY c.date_added DESC
                LIMIT " . (int)$limit;

            $query_result = $this->db->query($sql);

            $this->logDev("Customers::searchPartialMatches - Order search results: " . print_r($query_result->rows, true));

            foreach ($query_result->rows as $row) {
                $name = trim($row['firstname'] . ' ' . $row['lastname']);
                // Пропускаме записи с празни имена
                if (empty($name) || $name === ' ') {
                    continue;
                }

                $results[] = [
                    'customer_id' => $row['customer_id'],
                    'name' => $name,
                    'email' => $row['email'] ?? '',
                    'telephone' => $row['telephone'] ?? '',
                    'status' => $row['status'] ? 'Активен' : 'Неактивен',
                    'customer_group' => $row['customer_group'] ?? '',
                    'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                    'relevance' => 50, // По-ниска релевантност за частични резултати
                    'found_via_order' => $row['order_id']
                ];
            }

            // Ако намерихме клиент чрез поръчка, връщаме резултата
            if (!empty($results)) {
                $this->logDev("Customers::searchPartialMatches - Found customer via order_id: " . print_r($results, true));
                return $results;
            }
        }

        // Строим условията за търсене
        $searchConditions = [];
        foreach ($words as $word) {
            $escapedWord = $this->db->escape(mb_strtolower($word));
            $transliteratedWord = $this->transliterate($word);
            $escapedTranslit = $this->db->escape(mb_strtolower($transliteratedWord));

            $wordConditions = [
                "LOWER(c.firstname) LIKE '%{$escapedWord}%'",
                "LOWER(c.lastname) LIKE '%{$escapedWord}%'",
                "LOWER(c.email) LIKE '%{$escapedWord}%'",
                "LOWER(c.telephone) LIKE '%{$escapedWord}%'"
            ];

            // Добавяме транслитерирани варианти ако са различни
            if ($transliteratedWord !== $word) {
                $wordConditions[] = "LOWER(c.firstname) LIKE '%{$escapedTranslit}%'";
                $wordConditions[] = "LOWER(c.lastname) LIKE '%{$escapedTranslit}%'";
                $wordConditions[] = "LOWER(c.email) LIKE '%{$escapedTranslit}%'";
            }

            $searchConditions[] = '(' . implode(' OR ', $wordConditions) . ')';
        }

        $whereCondition = implode(' AND ', $searchConditions);

        // Добавяме изключване на вече намерените клиенти
        if (!empty($excludeIds)) {
            $excludeList = implode(',', array_map('intval', $excludeIds));
            $whereCondition .= " AND c.customer_id NOT IN ({$excludeList})";
        }

        $sql = "
            SELECT DISTINCT
                c.customer_id,
                c.firstname,
                c.lastname,
                c.email,
                c.telephone,
                c.date_added,
                c.status,
                cg.name as customer_group,
                'partial' as match_type
            FROM
                " . DB_PREFIX . "customer c
            LEFT JOIN
                " . DB_PREFIX . "customer_group_description cg ON (c.customer_group_id = cg.customer_group_id)
            WHERE
                cg.language_id = '" . (int)$language_id . "'
                AND (c.firstname IS NOT NULL AND c.firstname != ''
                     OR c.lastname IS NOT NULL AND c.lastname != '')
                AND ({$whereCondition})
            ORDER BY
                c.date_added DESC
            LIMIT " . (int)$limit;

        $query_result = $this->db->query($sql);

        foreach ($query_result->rows as $row) {
            $name = trim($row['firstname'] . ' ' . $row['lastname']);
            // Пропускаме записи с празни имена
            if (empty($name) || $name === ' ') {
                continue;
            }

            $results[] = [
                'customer_id' => $row['customer_id'],
                'name' => $name,
                'email' => $row['email'] ?? '',
                'telephone' => $row['telephone'] ?? '',
                'date_added' => date('d.m.Y H:i', strtotime($row['date_added'])),
                'customer_group' => $row['customer_group'] ?? '',
                'status' => $row['status'] ? 'Активен' : 'Неактивен',
                'relevance' => 50
            ];
        }

        return $results;
    }

    /**
     * Търсене във всички клиенти (за "виж всички" страница)
     */
    public function searchAll($query, $page = 1, $limit = 20) {
        // Проверяваме кеша първо
        $cacheKey = $this->generateCacheKey('customers_all', $query, $limit, ($page - 1) * $limit);
        $cachedResults = $this->getCachedResults($cacheKey);

        if ($cachedResults !== null) {
            return $cachedResults;
        }

        $offset = ($page - 1) * $limit;
        $results = $this->performOptimizedSearch($query, $limit, $offset);

        // Добавяме общия брой резултати за пагинация
        $total = $this->getTotalCount($query);

        $searchResults = [
            'results' => $results,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];

        // Кешираме резултатите
        $this->cacheResults($cacheKey, $searchResults);

        return $searchResults;
    }

    /**
     * Подготвя думите за търсене
     */
    private function prepareSearchWords($query) {
        $query = trim($query);
        if (empty($query)) {
            return [];
        }

        // Разделяме по интервали и премахваме празните елементи
        $words = array_filter(explode(' ', $query), function($word) {
            return !empty(trim($word));
        });

        return array_map('trim', $words);
    }

    /**
     * Транслитерира текст от кирилица към латиница и обратно
     */
    private function transliterate($text) {
        $text = mb_strtolower($text);

        // Първо проверяваме дали текстът е на кирилица
        if (preg_match('/[а-я]/u', $text)) {
            // Кирилица -> Латиница
            return strtr($text, $this->translitMap);
        } else {
            // Латиница -> Кирилица (обратна транслитерация)
            $reverseMap = array_flip($this->translitMap);

            // Специални случаи за многосимволни комбинации
            $specialCases = [
                'sht' => 'щ', 'zh' => 'ж', 'ch' => 'ч', 'sh' => 'ш', 'yu' => 'ю', 'ya' => 'я'
            ];

            foreach ($specialCases as $latin => $cyrillic) {
                $text = str_replace($latin, $cyrillic, $text);
            }

            return strtr($text, $reverseMap);
        }
    }



    /**
     * Получава общия брой резултати за дадена заявка
     */
    private function getTotalCount($query) {
        // Подготвяме думите за търсене
        $words = $this->prepareSearchWords($query);
        if (empty($words)) {
            return 0;
        }

        // Строим условията за търсене
        $searchConditions = [];
        foreach ($words as $word) {
            $escapedWord = $this->db->escape(mb_strtolower($word));
            $transliteratedWord = $this->transliterate($word);
            $escapedTranslit = $this->db->escape(mb_strtolower($transliteratedWord));

            $wordConditions = [
                "LOWER(c.firstname) LIKE '%{$escapedWord}%'",
                "LOWER(c.lastname) LIKE '%{$escapedWord}%'",
                "LOWER(c.email) LIKE '%{$escapedWord}%'",
                "LOWER(c.telephone) LIKE '%{$escapedWord}%'"
            ];

            // Добавяме транслитерирани варианти ако са различни
            if ($transliteratedWord !== $word) {
                $wordConditions[] = "LOWER(c.firstname) LIKE '%{$escapedTranslit}%'";
                $wordConditions[] = "LOWER(c.lastname) LIKE '%{$escapedTranslit}%'";
                $wordConditions[] = "LOWER(c.email) LIKE '%{$escapedTranslit}%'";
            }

            // Премахваме търсенето по customer_id за да избегнем конфликти с order_id търсенето
            // Числата се интерпретират като order_id, а не customer_id
            // if (is_numeric($word)) {
            //     $wordConditions[] = "c.customer_id = '" . (int)$word . "'";
            // }

            $searchConditions[] = '(' . implode(' OR ', $wordConditions) . ')';
        }

        $whereCondition = implode(' AND ', $searchConditions);

        $sql = "
            SELECT COUNT(DISTINCT c.customer_id) as total
            FROM " . DB_PREFIX . "customer c
            LEFT JOIN " . DB_PREFIX . "customer_group_description cg ON (c.customer_group_id = cg.customer_group_id)
            WHERE cg.language_id = '" . (int)$this->getLanguageId() . "'
            AND (c.firstname IS NOT NULL AND c.firstname != ''
                 OR c.lastname IS NOT NULL AND c.lastname != '')
            AND ({$whereCondition})";

        $result = $this->db->query($sql);
        return (int)$result->row['total'];
    }

    /**
     * Рендира визуализацията на списъка с клиенти
     */
    public function renderItems($results, $withPagination = false, $query = '', $page = 1, $limit = 20, $total = 0) {
        // Подготвяме данните за клиентите
        $customers = [];
        foreach ($results as $customer) {
            $customers[] = [
                'customer_id' => $customer['customer_id'],
                'name' => trim(($customer['firstname'] ?? '') . ' ' . ($customer['lastname'] ?? '')),
                'firstname' => $customer['firstname'] ?? '',
                'lastname' => $customer['lastname'] ?? '',
                'email' => $customer['email'] ?? '',
                'telephone' => $customer['telephone'] ?? '',
                'date_added' => $customer['date_added'] ?? '',
                'status' => ($customer['status'] ?? 0) == 1 ? 'Активен' : 'Неактивен',
                'status_class' => ($customer['status'] ?? 0) == 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800',
                'view' => $this->getAdminLink('sale/customer/info', '&customer_id=' . $customer['customer_id']),
                'edit' => $this->getAdminLink('sale/customer/edit', '&customer_id=' . $customer['customer_id'])
            ];
        }

        // Подготвяме данните за шаблона
        $data = [
            'customers' => $customers,
            'with_pagination' => $withPagination,
            'query' => $query,
            'page' => $page,
            'limit' => $limit,
            'total' => $total
        ];

        // Пагинация ако е необходима
        if ($withPagination && $total > $limit) {
            $pagination = new \Theme25\Pagination();
            $pagination->total = $total;
            $pagination->page = $page;
            $pagination->limit = $limit;
            $pagination->url = $this->getAdminLink('common/globalsearch/customers', '&query=' . urlencode($query) . '&page={page}');
            $pagination->setProductText('клиента');
            $data['pagination'] = $pagination->render();
        }

        // Рендираме шаблона и връщаме HTML
        return $this->renderTemplate('common/global_search_customers', $data);
    }
}
