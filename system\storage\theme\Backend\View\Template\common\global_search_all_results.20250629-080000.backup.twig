<!-- Search Results Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex flex-col md:flex-row md:items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800 flex items-center">
                <i class="ri-search-line mr-2"></i>
                Резултати от търсене: "{{ query }}"
            </h1>
            <p class="text-gray-500 mt-1">Всички резултати</p>
        </div>
        <div class="mt-4 md:mt-0 flex items-center space-x-2">
            <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">Всички групи</span>
            <span class="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm font-medium">{{ total }} резултата</span>
        </div>
    </div>
</div>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
    {% if error %}
        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div class="flex items-center">
                <i class="ri-error-warning-line text-red-500 mr-2"></i>
                <span class="text-red-700">{{ error }}</span>
            </div>
        </div>
    {% elseif (results.orders and results.orders|length > 0) or (results.customers and results.customers|length > 0) or (results.products and results.products|length > 0) or (results.categories and results.categories|length > 0) or (group_counts.orders and group_counts.orders > 0) or (group_counts.customers and group_counts.customers > 0) or (group_counts.products and group_counts.products > 0) or (group_counts.categories and group_counts.categories > 0) %}
        <!-- Group Navigation Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {% if group_counts.orders and group_counts.orders > 0 %}
            <a href="index.php?route=common/globalsearch/orders&query={{ query|url_encode }}&user_token={{ user_token }}" class="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 border-l-4 border-blue-500">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <i class="ri-shopping-cart-line text-blue-600"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Поръчки</h3>
                        <p class="text-sm text-gray-600">{{ group_counts.orders }} резултата</p>
                    </div>
                </div>
            </a>
            {% endif %}

            {% if group_counts.customers and group_counts.customers > 0 %}
            <a href="index.php?route=common/globalsearch/customers&query={{ query|url_encode }}&user_token={{ user_token }}" class="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 border-l-4 border-green-500">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <i class="ri-user-line text-green-600"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Клиенти</h3>
                        <p class="text-sm text-gray-600">{{ group_counts.customers }} резултата</p>
                    </div>
                </div>
            </a>
            {% endif %}

            {% if group_counts.products and group_counts.products > 0 %}
            <a href="index.php?route=common/globalsearch/products&query={{ query|url_encode }}&user_token={{ user_token }}" class="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 border-l-4 border-purple-500">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                            <i class="ri-box-line text-purple-600"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Продукти</h3>
                        <p class="text-sm text-gray-600">{{ group_counts.products }} резултата</p>
                    </div>
                </div>
            </a>
            {% endif %}

            {% if group_counts.categories and group_counts.categories > 0 %}
            <a href="index.php?route=common/globalsearch/categories&query={{ query|url_encode }}&user_token={{ user_token }}" class="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 border-l-4 border-orange-500">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                            <i class="ri-folder-line text-orange-600"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-semibold text-gray-900">Категории</h3>
                        <p class="text-sm text-gray-600">{{ group_counts.categories }} резултата</p>
                    </div>
                </div>
            </a>
            {% endif %}
        </div>

        <!-- Orders Section -->
        {% if orders and orders|length > 0 %}
        <div class="bg-white rounded shadow mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i class="ri-shopping-cart-line text-blue-600 mr-2"></i>
                    Поръчки ({{ orders|length }})
                </h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <div class="flex items-center">
                                    <input type="checkbox" class="mr-2">
                                    Номер
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Дата
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Клиент
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Начин на плащане
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Статус
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Сума
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Действия
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for order in orders %}
                        <tr class="hover:bg-gray-50" data-order-id="{{ order.order_id }}">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <input type="checkbox" name="selected[]" value="{{ order.order_id }}" class="mr-2">
                                    <span class="text-sm font-medium text-gray-900">#{{ order.order_id }}</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ order.date_added }}</div>
                                {% if order.date_modified %}
                                <div class="text-sm text-gray-500">Изм: {{ order.date_modified }}</div>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">{{ order.customer_name }}</div>
                                {% if order.email %}
                                <div class="text-sm text-gray-500">{{ order.email }}</div>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ order.payment_method }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ order.status_class }}">
                                    {{ order.order_status }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ order.total }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-2">
                                    <a href="{{ order.view }}" class="text-blue-600 hover:text-blue-900" title="Преглед">
                                        <i class="ri-eye-line"></i>
                                    </a>
                                    <a href="{{ order.edit }}" class="text-primary hover:text-primary/80" title="Редактирай">
                                        <i class="ri-edit-line"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}

        <!-- Customers Section -->
        {% if customers and customers|length > 0 %}
        <div class="bg-white rounded shadow mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i class="ri-user-line text-green-600 mr-2"></i>
                    Клиенти ({{ customers|length }})
                </h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <div class="flex items-center">
                                    <input type="checkbox" class="mr-2">
                                    Клиент
                                </div>
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Имейл
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Телефон
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Брой поръчки
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Обща стойност
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Статус
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Действия
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for customer in customers %}
                        <tr class="hover:bg-gray-50" data-customer-id="{{ customer.customer_id }}">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <input type="checkbox" name="selected[]" value="{{ customer.customer_id }}" class="mr-2">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ customer.name }}</div>
                                        <div class="text-sm text-gray-500">ID: {{ customer.customer_id }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ customer.email }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ customer.telephone|default('N/A') }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ customer.order_count|default(0) }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ customer.total_spent|default('0.00') }} лв.</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ customer.status_class }}">
                                    {{ customer.status }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-2">
                                    <a href="{{ customer.view }}" class="text-blue-600 hover:text-blue-900" title="Преглед">
                                        <i class="ri-eye-line"></i>
                                    </a>
                                    <a href="{{ customer.edit }}" class="text-primary hover:text-primary/80" title="Редактирай">
                                        <i class="ri-edit-line"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}

        <!-- Products Section -->
        {% if products and products|length > 0 %}
        <div class="bg-white rounded shadow mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i class="ri-box-line text-purple-600 mr-2"></i>
                    Продукти ({{ products|length }})
                </h3>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 p-6">
                {% for product in products %}
                <div class="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                    <div class="relative">
                        <div class="aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-t-lg bg-gray-200">
                            {% if product.image %}
                                <img src="{{ product.image }}" alt="{{ product.name }}" class="h-48 w-full object-cover object-center">
                            {% else %}
                                <div class="h-48 w-full bg-gray-100 flex items-center justify-center">
                                    <i class="ri-image-line text-gray-400 text-4xl"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="absolute top-2 right-2">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ product.status_class }}">
                                {{ product.status }}
                            </span>
                        </div>
                    </div>
                    <div class="p-4">
                        <h3 class="text-sm font-medium text-gray-900 mb-2 line-clamp-2">{{ product.name }}</h3>
                        <div class="text-xs text-gray-500 mb-2">
                            <div>ID: {{ product.product_id }}</div>
                            <div>Модел: {{ product.model|default('N/A') }}</div>
                        </div>
                        <div class="flex items-center justify-between mb-3">
                            <div class="text-sm font-medium text-gray-900">{{ product.price }} лв.</div>
                            <div class="text-xs text-gray-500">Наличност: {{ product.quantity|default(0) }}</div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex space-x-2">
                                <a href="{{ product.view }}" class="text-blue-600 hover:text-blue-900" title="Преглед">
                                    <i class="ri-eye-line"></i>
                                </a>
                                <a href="{{ product.edit }}" class="text-primary hover:text-primary/80" title="Редактирай">
                                    <i class="ri-edit-line"></i>
                                </a>
                            </div>
                            <input type="checkbox" name="selected[]" value="{{ product.product_id }}" class="rounded">
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Categories Section -->
        {% if categories and categories|length > 0 %}
        <div class="bg-white rounded shadow mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                    <i class="ri-folder-line text-orange-600 mr-2"></i>
                    Категории ({{ categories|length }})
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    {% for category in categories %}
                    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 hover:bg-gray-100 transition-colors">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <input type="checkbox" name="selected[]" value="{{ category.category_id }}" class="rounded">
                                <div class="flex items-center space-x-2">
                                    <i class="ri-drag-move-line text-gray-400 cursor-move"></i>
                                    <div class="w-12 h-12 bg-white border border-gray-200 rounded-lg flex items-center justify-center">
                                        {% if category.image %}
                                            <img src="{{ category.image }}" alt="{{ category.name }}" class="w-10 h-10 object-cover rounded">
                                        {% else %}
                                            <i class="ri-folder-line text-gray-400"></i>
                                        {% endif %}
                                    </div>
                                </div>
                                <div>
                                    <h4 class="text-sm font-medium text-gray-900">{{ category.name }}</h4>
                                    <div class="text-xs text-gray-500">
                                        ID: {{ category.category_id }} | Сортиране: {{ category.sort_order|default(0) }}
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ category.status_class }}">
                                    {{ category.status }}
                                </span>
                                <div class="flex space-x-2">
                                    <a href="{{ category.view }}" class="text-blue-600 hover:text-blue-900" title="Преглед">
                                        <i class="ri-eye-line"></i>
                                    </a>
                                    <a href="{{ category.edit }}" class="text-primary hover:text-primary/80" title="Редактирай">
                                        <i class="ri-edit-line"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Pagination -->
        {% if pagination %}
        <div class="bg-white rounded shadow p-6">
            {{ pagination|raw }}
        </div>
        {% endif %}
    {% else %}
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex items-center">
                <i class="ri-information-line text-yellow-500 mr-2"></i>
                <span class="text-yellow-700">Няма намерени резултати за "{{ query }}"</span>
            </div>
        </div>
    {% endif %}
</main>
