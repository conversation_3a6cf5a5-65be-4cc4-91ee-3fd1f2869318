<?php

namespace Theme25\Backend\Controller\Common;

class Header extends \Theme25\Controller {

    public function __construct($registry) {
        parent::__construct($registry, 'common/header');
    }

    public function index() {

        $this->response->addHeader('Cache-Control: no-store, no-cache, must-revalidate');
        $this->response->addHeader('Cache-Control: post-check=0, pre-check=0', false);
        $this->response->addHeader('Pragma: no-cache');
        $this->response->addHeader('Expires: Thu, 19 Nov 1981 08:52:00 GMT');


        $this->setTitle($this->getLanguageText('heading_title'));

        // Инициализиране на данните
        $this->initAdminData();

        // Подготовка на данните с верижно извикване на методи
        $this->prepareUserData()
             ->prepareStoreData()
             ->prepareOtherData();

        // Рендиране на шаблона с данните от $this->data
        return $this->loadView('common/header', $this->data);
    }

    

    /**
     * Връща името на потребителя
     *
     * @return string Име на потребителя
     */
    private function getUserName() {
        return $this->user->getUserName();
    }

    /**
     * Връща ID на потребителя
     *
     * @return int ID на потребителя
     */
    private function getUserId() {
        return $this->user->getId();
    }

    /**
     * Подготвя данните за потребителя
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareUserData() {
        if (!$this->isUserLogged()) {
            $this->setData([
                'logged' => '',
                'home' => $this->getAdminLink('common/dashboard')
            ]);

            return $this;
        }

        // Потребителят е логнат
        $this->setData([
            'logged' => true,
            'home' => HTTPS_CATALOG,
            'logout' => $this->getAdminLink('common/logout'),
            'profile' => $this->getAdminLink('common/profile')
        ]);

        // Зареждане на необходимите модели
        $this->loadModelsAs([
            'user/user' => 'userModel',
            'tool/image' => 'image'
        ]);

        // Извличане на информация за потребителя
        $user_info = $this->userModel->getUser($this->getUserId());

        if ($user_info) {
            $image = is_file(DIR_IMAGE . $user_info['image'])
                ? $this->image->resize($user_info['image'], 45, 45)
                : $this->image->resize('profile.png', 45, 45);

            $this->setData([
                'firstname' => $user_info['firstname'],
                'lastname' => $user_info['lastname'],
                'username' => $user_info['username'],
                'user_group' => $user_info['user_group'],
                'image' => $image,
                'notifications' => 0
            ]);
        } else {
            $this->setData([
                'firstname' => '',
                'lastname' => '',
                'user_group' => '',
                'image' => ''
            ]);
        }

        return $this;
    }

    /**
     * Подготвя данните за магазините
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareStoreData() {
        // Проверка дали потребителят е логнат
        if (!$this->isUserLogged()) {
            return $this;
        }

        $stores = [
            [
                'name' => $this->getConfig('config_name'),
                'href' => HTTP_CATALOG
            ]
        ];

        // Зареждане на модела за магазини
        $this->loadModelAs('setting/store', 'store');

        // Извличане на всички магазини
        $results = $this->store->getStores();

        foreach ($results as $result) {
            $stores[] = [
                'name' => $result['name'],
                'href' => $result['url']
            ];
        }

        // Добавяне на магазините към $this->data
        $this->setData('stores', $stores);

        return $this;
    }

    /**
     * Подготвя другите данни
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareOtherData() {
        // Добавяне на други данни
        $this->setData([
            'search_query' => $this->requestGet('query'),
            'extended_search' => (int)$this->requestGet('extended_search', 0)
        ]);

        return $this;
    }
}